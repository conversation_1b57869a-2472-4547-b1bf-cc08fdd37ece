# نظام إدارة المخيم - Camp Management System

نظام شامل لإدارة مخيمات اللاجئين مطور بلغة Python باستخدام PyQt5.

## الميزات الرئيسية

### 🔐 نظام المصادقة والصلاحيات
- تسجيل دخول آمن مع تشفير كلمات المرور
- نظام صلاحيات قائم على الأدوار:
  - **مدير**: وصول كامل لجميع الميزات
  - **مدخل بيانات**: إضافة وتعديل بيانات الأسر
  - **مسؤول توزيع**: عرض البيانات وتسجيل توزيع المساعدات
  - **زائر**: وصول للقراءة فقط

### 👨‍👩‍👧‍👦 إدارة الأسر والأفراد
- إضافة وتعديل وأرشفة الأسر
- تسجيل معلومات مفصلة لكل أسرة:
  - بيانات رب الأسرة
  - معلومات السكن والوصول
  - الاحتياجات الخاصة والحالات الطبية
- إدارة أفراد الأسرة مع تتبع الأعمار والعلاقات
- بحث متقدم وفلترة البيانات

### 📊 التقارير والإحصائيات
- لوحة تحكم تفاعلية مع إحصائيات فورية
- رسوم بيانية للتوزيع العمري والجنسي
- تقارير مخصصة قابلة للتصدير
- إحصائيات الوافدين الجدد

### 📁 استيراد وتصدير Excel
- استيراد البيانات من ملفات Excel
- تصدير التقارير إلى Excel مع تنسيق احترافي
- قوالب جاهزة للاستيراد
- التحقق من صحة البيانات عند الاستيراد

### 🎨 واجهة مستخدم حديثة
- دعم كامل للغة العربية (RTL)
- تصميم نظيف وسهل الاستخدام
- واجهة متجاوبة مع أحجام شاشات مختلفة
- أيقونات وألوان واضحة

## متطلبات النظام

- Windows 10 أو أحدث
- 4 GB RAM (الحد الأدنى)
- 500 MB مساحة تخزين فارغة
- دقة شاشة 1024x768 أو أعلى

## التثبيت والتشغيل

### الطريقة الأولى: تشغيل الملف التنفيذي (موصى به)
1. قم بتحميل ملف `CampManagementSystem.exe`
2. قم بتشغيل الملف مباشرة
3. استخدم بيانات المدير الافتراضي:
   - **اسم المستخدم**: `admin`
   - **كلمة المرور**: `admin123`

### الطريقة الثانية: تشغيل من الكود المصدري
1. تأكد من تثبيت Python 3.8 أو أحدث
2. استنسخ المشروع:
   ```bash
   git clone [repository-url]
   cd Qatar-Camp
   ```
3. ثبت المتطلبات:
   ```bash
   pip install -r requirements.txt
   ```
4. شغل التطبيق:
   ```bash
   python src/main.py
   ```

## بناء الملف التنفيذي

لبناء ملف تنفيذي جديد:

```bash
python build.py
```

سيتم إنشاء الملف التنفيذي في مجلد `dist/`.

## هيكل المشروع

```
Qatar Camp/
├── src/                    # الكود المصدري
│   ├── config/            # إعدادات التطبيق وقاعدة البيانات
│   ├── models/            # نماذج قاعدة البيانات
│   ├── services/          # خدمات العمل
│   ├── gui/               # واجهات المستخدم
│   ├── utils/             # أدوات مساعدة
│   └── main.py           # نقطة البداية
├── resources/             # الموارد (أيقونات، قوالب)
├── database/             # قاعدة البيانات
├── requirements.txt      # متطلبات Python
├── build.py             # سكريبت البناء
└── README.md           # هذا الملف
```

## الاستخدام

### تسجيل الدخول الأول
1. شغل التطبيق
2. استخدم بيانات المدير الافتراضي
3. **مهم**: غير كلمة المرور فوراً من قائمة الملف

### إضافة أسرة جديدة
1. اذهب إلى تبويب "إدارة الأسر"
2. اضغط "إضافة أسرة"
3. املأ البيانات المطلوبة
4. احفظ البيانات

### استيراد البيانات من Excel
1. حمل قالب Excel من "تحميل قالب"
2. املأ البيانات في القالب
3. استخدم "استيراد Excel" لرفع الملف
4. راجع البيانات وأكد الاستيراد

### إنشاء التقارير
1. اذهب إلى تبويب "التقارير والإحصائيات"
2. اختر نوع التقرير المطلوب
3. حدد المعايير إذا لزم الأمر
4. صدر التقرير إلى Excel

## الأمان

- جميع كلمات المرور مشفرة باستخدام bcrypt
- نظام صلاحيات محكم لحماية البيانات الحساسة
- تسجيل عمليات تسجيل الدخول
- نسخ احتياطية تلقائية لقاعدة البيانات

## النسخ الاحتياطية

يتم حفظ البيانات في ملف `database/camp.db`. لإنشاء نسخة احتياطية:
1. أغلق التطبيق
2. انسخ ملف `camp.db` إلى مكان آمن
3. لاستعادة النسخة الاحتياطية، استبدل الملف الحالي

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

**التطبيق لا يبدأ:**
- تأكد من وجود صلاحيات الكتابة في مجلد التطبيق
- تأكد من عدم تشغيل نسخة أخرى من التطبيق

**خطأ في قاعدة البيانات:**
- تأكد من وجود مجلد `database`
- تحقق من صلاحيات الكتابة
- جرب حذف ملف `camp.db` لإعادة إنشائه

**مشاكل في استيراد Excel:**
- تأكد من تنسيق الملف (.xlsx)
- استخدم القالب المتوفر
- تحقق من صحة البيانات في الملف

## التطوير والمساهمة

### إعداد بيئة التطوير
1. استنسخ المشروع
2. أنشئ بيئة افتراضية:
   ```bash
   python -m venv venv
   venv\Scripts\activate  # Windows
   ```
3. ثبت المتطلبات:
   ```bash
   pip install -r requirements.txt
   ```

### إضافة ميزات جديدة
1. أنشئ فرع جديد للميزة
2. اتبع هيكل المشروع الحالي
3. أضف اختبارات للميزات الجديدة
4. حدث الوثائق

## الترخيص

هذا المشروع مطور لأغراض إنسانية ومتاح للاستخدام المجاني في مخيمات اللاجئين والمنظمات الإنسانية.

## الدعم

للحصول على الدعم الفني أو الإبلاغ عن مشاكل:
- أنشئ issue في المستودع
- تواصل مع فريق التطوير

## الإصدارات

### الإصدار 1.0.0
- إدارة الأسر والأفراد
- نظام المصادقة والصلاحيات
- استيراد وتصدير Excel
- التقارير والإحصائيات الأساسية
- واجهة مستخدم باللغة العربية

### خطط مستقبلية
- وحدة توزيع المساعدات
- تقارير متقدمة
- نظام الإشعارات
- تكامل مع أنظمة خارجية
- تطبيق ويب مصاحب

---

**تم تطوير هذا النظام بعناية لخدمة المجتمعات المحتاجة. نأمل أن يساهم في تحسين إدارة المخيمات وخدمة اللاجئين.**
