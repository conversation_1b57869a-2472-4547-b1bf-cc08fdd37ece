"""
سكريبت بناء التطبيق إلى ملف تنفيذي
Build Script for Creating Executable
"""
import os
import sys
import shutil
import subprocess
from pathlib import Path

def clean_build_directories():
    """تنظيف مجلدات البناء السابقة"""
    directories_to_clean = ['build', 'dist', '__pycache__']
    
    for directory in directories_to_clean:
        if os.path.exists(directory):
            print(f"تنظيف مجلد: {directory}")
            shutil.rmtree(directory)
    
    # تنظيف ملفات .pyc
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.pyc'):
                os.remove(os.path.join(root, file))

def install_requirements():
    """تثبيت المتطلبات"""
    print("تثبيت المتطلبات...")
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
        print("تم تثبيت المتطلبات بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"فشل في تثبيت المتطلبات: {e}")
        return False

def create_spec_file():
    """إنشاء ملف .spec لـ PyInstaller"""
    spec_content = '''
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['src/main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('resources', 'resources'),
    ],
    hiddenimports=[
        'PyQt5.QtCore',
        'PyQt5.QtGui', 
        'PyQt5.QtWidgets',
        'sqlalchemy.sql.default_comparator',
        'sqlalchemy.ext.baked',
        'matplotlib.backends.backend_qt5agg',
        'openpyxl',
        'pandas',
        'bcrypt',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='CampManagementSystem',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
'''
    
    with open('camp_management.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("تم إنشاء ملف .spec")

def build_executable():
    """بناء الملف التنفيذي"""
    print("بناء الملف التنفيذي...")
    
    try:
        # إنشاء ملف .spec
        create_spec_file()
        
        # تشغيل PyInstaller
        cmd = [
            sys.executable, '-m', 'PyInstaller',
            '--clean',
            '--noconfirm',
            'camp_management.spec'
        ]
        
        subprocess.check_call(cmd)
        print("تم بناء الملف التنفيذي بنجاح!")
        
        # نسخ الملفات الإضافية
        copy_additional_files()
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"فشل في بناء الملف التنفيذي: {e}")
        return False

def copy_additional_files():
    """نسخ الملفات الإضافية إلى مجلد التوزيع"""
    dist_dir = Path('dist')
    
    # إنشاء مجلد resources إذا لم يكن موجوداً
    resources_dir = dist_dir / 'resources'
    resources_dir.mkdir(exist_ok=True)
    
    # إنشاء مجلد database
    database_dir = dist_dir / 'database'
    database_dir.mkdir(exist_ok=True)
    
    # إنشاء ملف README
    readme_content = """
نظام إدارة المخيم - Camp Management System
==========================================

تعليمات التشغيل:
1. قم بتشغيل ملف CampManagementSystem.exe
2. استخدم بيانات المدير الافتراضي:
   - اسم المستخدم: admin
   - كلمة المرور: admin123

ملاحظات:
- يرجى تغيير كلمة مرور المدير بعد أول تسجيل دخول
- يتم حفظ البيانات في مجلد database
- يمكن نسخ احتياطية من ملف camp.db

للدعم الفني، يرجى التواصل مع فريق التطوير.
"""
    
    with open(dist_dir / 'README.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("تم نسخ الملفات الإضافية")

def create_installer():
    """إنشاء ملف تثبيت (اختياري)"""
    print("إنشاء ملف التثبيت...")
    
    # يمكن استخدام NSIS أو Inno Setup لإنشاء installer
    # هذا مثال بسيط لضغط الملفات
    
    try:
        import zipfile
        
        with zipfile.ZipFile('CampManagementSystem_v1.0.zip', 'w', zipfile.ZIP_DEFLATED) as zipf:
            # إضافة جميع ملفات dist
            for root, dirs, files in os.walk('dist'):
                for file in files:
                    file_path = os.path.join(root, file)
                    arcname = os.path.relpath(file_path, 'dist')
                    zipf.write(file_path, arcname)
        
        print("تم إنشاء ملف مضغوط: CampManagementSystem_v1.0.zip")
        return True
        
    except Exception as e:
        print(f"فشل في إنشاء ملف التثبيت: {e}")
        return False

def main():
    """الدالة الرئيسية للبناء"""
    print("=== بناء نظام إدارة المخيم ===")
    print()
    
    # التحقق من وجود Python
    print(f"إصدار Python: {sys.version}")
    
    # تنظيف المجلدات السابقة
    clean_build_directories()
    
    # تثبيت المتطلبات
    if not install_requirements():
        print("فشل في تثبيت المتطلبات. توقف البناء.")
        return 1
    
    # بناء الملف التنفيذي
    if not build_executable():
        print("فشل في بناء الملف التنفيذي. توقف البناء.")
        return 1
    
    # إنشاء ملف التثبيت
    create_installer()
    
    print()
    print("=== اكتمل البناء بنجاح! ===")
    print("يمكنك العثور على الملف التنفيذي في مجلد 'dist'")
    print("الملف المضغوط: CampManagementSystem_v1.0.zip")
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
