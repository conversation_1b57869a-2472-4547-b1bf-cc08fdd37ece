"""
سكريبت تحويل التطبيق إلى ملف تنفيذي
Build Script for Creating Executable
"""
import os
import sys
import shutil
import subprocess
from pathlib import Path

def install_pyinstaller():
    """تثبيت PyInstaller"""
    print("🔧 تثبيت PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✅ تم تثبيت PyInstaller بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت PyInstaller: {e}")
        return False

def create_spec_file():
    """إنشاء ملف المواصفات لـ PyInstaller"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['tkinter_app.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('src', 'src'),
        ('data', 'data'),
        ('resources', 'resources'),
    ],
    hiddenimports=[
        'tkinter',
        'tkinter.ttk',
        'sqlalchemy',
        'pandas',
        'openpyxl',
        'dateutil',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='CampManagementSystem',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='resources/icons/app.ico' if os.path.exists('resources/icons/app.ico') else None,
)
'''
    
    with open('camp_management.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ تم إنشاء ملف المواصفات")

def prepare_resources():
    """تحضير الموارد المطلوبة"""
    print("📁 تحضير الموارد...")
    
    # إنشاء مجلد الموارد
    os.makedirs('resources/icons', exist_ok=True)
    os.makedirs('data', exist_ok=True)
    
    # إنشاء أيقونة افتراضية (نص فقط)
    icon_info = """
ملاحظة: لم يتم العثور على أيقونة التطبيق.
يمكنك إضافة ملف app.ico في مجلد resources/icons/
لتخصيص أيقونة التطبيق.
"""
    
    if not os.path.exists('resources/icons/app.ico'):
        with open('resources/icons/icon_info.txt', 'w', encoding='utf-8') as f:
            f.write(icon_info)
    
    print("✅ تم تحضير الموارد")

def build_executable():
    """بناء الملف التنفيذي"""
    print("🔨 بناء الملف التنفيذي...")
    
    try:
        # تشغيل PyInstaller
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--clean",
            "--noconfirm",
            "camp_management.spec"
        ]
        
        print(f"تشغيل الأمر: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ تم بناء الملف التنفيذي بنجاح")
            return True
        else:
            print(f"❌ فشل في البناء:")
            print(result.stdout)
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ خطأ في البناء: {e}")
        return False

def create_installer_script():
    """إنشاء سكريبت المثبت"""
    installer_content = '''@echo off
echo ===================================
echo    نظام إدارة المخيم - المثبت
echo    Camp Management System Installer
echo ===================================
echo.

echo جاري تثبيت النظام...
echo Installing system...

REM إنشاء مجلد التطبيق
if not exist "C:\\CampManagement" mkdir "C:\\CampManagement"

REM نسخ الملفات
xcopy /E /I /Y "dist\\CampManagementSystem.exe" "C:\\CampManagement\\"
xcopy /E /I /Y "data" "C:\\CampManagement\\data\\"

REM إنشاء اختصار على سطح المكتب
echo Set oWS = WScript.CreateObject("WScript.Shell") > CreateShortcut.vbs
echo sLinkFile = "%USERPROFILE%\\Desktop\\نظام إدارة المخيم.lnk" >> CreateShortcut.vbs
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> CreateShortcut.vbs
echo oLink.TargetPath = "C:\\CampManagement\\CampManagementSystem.exe" >> CreateShortcut.vbs
echo oLink.WorkingDirectory = "C:\\CampManagement" >> CreateShortcut.vbs
echo oLink.Description = "نظام إدارة المخيم" >> CreateShortcut.vbs
echo oLink.Save >> CreateShortcut.vbs
cscript CreateShortcut.vbs
del CreateShortcut.vbs

echo.
echo ✅ تم تثبيت النظام بنجاح!
echo ✅ System installed successfully!
echo.
echo يمكنك الآن تشغيل النظام من سطح المكتب
echo You can now run the system from desktop
echo.
pause
'''
    
    with open('installer.bat', 'w', encoding='utf-8') as f:
        f.write(installer_content)
    
    print("✅ تم إنشاء سكريبت المثبت")

def create_readme():
    """إنشاء ملف README"""
    readme_content = '''# نظام إدارة المخيم
## Camp Management System

### معلومات النظام
- الإصدار: 1.0.0
- تاريخ البناء: {build_date}
- نظام التشغيل: Windows 10/11

### متطلبات النظام
- Windows 10 أو أحدث
- 4GB RAM (مستحسن)
- 500MB مساحة فارغة

### التثبيت
1. شغل ملف installer.bat كمدير
2. اتبع التعليمات على الشاشة
3. ستجد اختصار النظام على سطح المكتب

### تشغيل النظام
- انقر مرتين على اختصار "نظام إدارة المخيم" على سطح المكتب
- أو شغل الملف CampManagementSystem.exe مباشرة

### بيانات المدير الافتراضي
- اسم المستخدم: admin
- كلمة المرور: admin123

### الميزات
✅ إدارة الأسر والأفراد
✅ نظام صلاحيات متقدم
✅ تصدير واستيراد Excel
✅ تقارير وإحصائيات
✅ إدارة المستخدمين
✅ واجهة عربية كاملة

### الدعم الفني
في حالة وجود مشاكل، يرجى التواصل مع فريق الدعم.

### حقوق الطبع
© 2024 نظام إدارة المخيم. جميع الحقوق محفوظة.
'''.format(build_date=__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    
    with open('README.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ تم إنشاء ملف README")

def cleanup():
    """تنظيف الملفات المؤقتة"""
    print("🧹 تنظيف الملفات المؤقتة...")
    
    temp_dirs = ['build', '__pycache__']
    temp_files = ['camp_management.spec']
    
    for temp_dir in temp_dirs:
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)
            print(f"✅ تم حذف {temp_dir}")
    
    for temp_file in temp_files:
        if os.path.exists(temp_file):
            os.remove(temp_file)
            print(f"✅ تم حذف {temp_file}")

def create_distribution_package():
    """إنشاء حزمة التوزيع"""
    print("📦 إنشاء حزمة التوزيع...")
    
    # إنشاء مجلد التوزيع
    dist_dir = "CampManagement_Distribution"
    if os.path.exists(dist_dir):
        shutil.rmtree(dist_dir)
    
    os.makedirs(dist_dir)
    
    # نسخ الملفات المطلوبة
    files_to_copy = [
        ('dist/CampManagementSystem.exe', 'CampManagementSystem.exe'),
        ('installer.bat', 'installer.bat'),
        ('README.txt', 'README.txt'),
    ]
    
    for src, dst in files_to_copy:
        if os.path.exists(src):
            shutil.copy2(src, os.path.join(dist_dir, dst))
            print(f"✅ تم نسخ {src}")
    
    # نسخ مجلد البيانات
    if os.path.exists('data'):
        shutil.copytree('data', os.path.join(dist_dir, 'data'))
        print("✅ تم نسخ مجلد البيانات")
    
    print(f"✅ تم إنشاء حزمة التوزيع في: {dist_dir}")

def main():
    """الدالة الرئيسية"""
    print("🏕️ نظام إدارة المخيم - أداة البناء والتعبئة")
    print("=" * 60)
    
    # التحقق من وجود الملف الرئيسي
    if not os.path.exists('tkinter_app.py'):
        print("❌ لم يتم العثور على tkinter_app.py")
        print("تأكد من تشغيل هذا السكريبت في مجلد المشروع الرئيسي")
        return 1
    
    steps = [
        ("تثبيت PyInstaller", install_pyinstaller),
        ("تحضير الموارد", prepare_resources),
        ("إنشاء ملف المواصفات", create_spec_file),
        ("بناء الملف التنفيذي", build_executable),
        ("إنشاء سكريبت المثبت", create_installer_script),
        ("إنشاء ملف README", create_readme),
        ("إنشاء حزمة التوزيع", create_distribution_package),
        ("تنظيف الملفات المؤقتة", cleanup),
    ]
    
    for step_name, step_func in steps:
        print(f"\n🔄 {step_name}...")
        try:
            if not step_func():
                print(f"❌ فشل في: {step_name}")
                return 1
        except Exception as e:
            print(f"❌ خطأ في {step_name}: {e}")
            return 1
    
    print("\n" + "=" * 60)
    print("🎉 تم إنجاز عملية البناء والتعبئة بنجاح!")
    print("\n📁 الملفات الجاهزة:")
    print("   - CampManagement_Distribution/ (حزمة التوزيع الكاملة)")
    print("   - dist/CampManagementSystem.exe (الملف التنفيذي)")
    print("   - installer.bat (المثبت)")
    print("   - README.txt (دليل الاستخدام)")
    
    print("\n📋 الخطوات التالية:")
    print("1. اختبر الملف التنفيذي: dist/CampManagementSystem.exe")
    print("2. وزع حزمة التوزيع: CampManagement_Distribution/")
    print("3. شارك ملف installer.bat مع المستخدمين")
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    input("\nاضغط Enter للخروج...")
    sys.exit(exit_code)
