"""
نسخة وحدة التحكم من نظام إدارة المخيم
Console Version of Camp Management System
"""
import sys
import os
from datetime import datetime, date

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.config.database import init_database, create_tables
from src.config.settings import create_directories
from src.services.auth_service import AuthService
from src.services.family_service import FamilyService
from src.services.excel_service import ExcelService

class CampManagementConsole:
    """نسخة وحدة التحكم من نظام إدارة المخيم"""
    
    def __init__(self):
        self.auth_service = AuthService()
        self.family_service = FamilyService()
        self.excel_service = ExcelService()
        self.current_user = None
        
    def setup(self):
        """إعداد النظام"""
        print("🏕️ نظام إدارة المخيم - إصدار وحدة التحكم")
        print("=" * 50)
        
        try:
            # إنشاء المجلدات
            create_directories()
            print("✓ تم إنشاء المجلدات")
            
            # تهيئة قاعدة البيانات
            init_database()
            create_tables()
            print("✓ تم إعداد قاعدة البيانات")
            
            # إنشاء المدير الافتراضي
            self.auth_service.create_default_admin()
            print("✓ تم إعداد المدير الافتراضي")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في الإعداد: {e}")
            return False
    
    def login(self):
        """تسجيل الدخول"""
        print("\n--- تسجيل الدخول ---")
        
        max_attempts = 3
        attempts = 0
        
        while attempts < max_attempts:
            username = input("اسم المستخدم: ").strip()
            password = input("كلمة المرور: ").strip()
            
            success, message, user = self.auth_service.login(username, password)
            
            if success:
                self.current_user = user
                print(f"✓ {message}")
                return True
            else:
                attempts += 1
                remaining = max_attempts - attempts
                print(f"❌ {message}")
                if remaining > 0:
                    print(f"المحاولات المتبقية: {remaining}")
                
        print("❌ تم استنفاد المحاولات المسموحة")
        return False
    
    def show_main_menu(self):
        """عرض القائمة الرئيسية"""
        while True:
            print(f"\n--- القائمة الرئيسية ---")
            print(f"مرحباً {self.current_user.full_name} ({self.current_user.get_role_display()})")
            print()
            
            menu_options = []
            
            if self.auth_service.has_permission("read"):
                menu_options.extend([
                    ("1", "عرض الإحصائيات"),
                    ("2", "عرض الأسر"),
                    ("3", "البحث عن أسرة")
                ])
            
            if self.auth_service.has_permission("create"):
                menu_options.append(("4", "إضافة أسرة جديدة"))
            
            if self.auth_service.has_permission("reports"):
                menu_options.extend([
                    ("5", "تصدير البيانات إلى Excel"),
                    ("6", "إنشاء قالب Excel")
                ])
            
            menu_options.extend([
                ("7", "تغيير كلمة المرور"),
                ("0", "تسجيل الخروج")
            ])
            
            for option, description in menu_options:
                print(f"{option}. {description}")
            
            choice = input("\nاختر رقماً: ").strip()
            
            if choice == "1":
                self.show_statistics()
            elif choice == "2":
                self.show_families()
            elif choice == "3":
                self.search_families()
            elif choice == "4" and self.auth_service.has_permission("create"):
                self.add_family()
            elif choice == "5" and self.auth_service.has_permission("reports"):
                self.export_to_excel()
            elif choice == "6" and self.auth_service.has_permission("reports"):
                self.create_excel_template()
            elif choice == "7":
                self.change_password()
            elif choice == "0":
                print("تم تسجيل الخروج بنجاح")
                break
            else:
                print("❌ خيار غير صحيح")
    
    def show_statistics(self):
        """عرض الإحصائيات"""
        print("\n--- الإحصائيات ---")
        
        try:
            stats = self.family_service.get_statistics()
            
            print(f"إجمالي الأسر: {stats['total_families']}")
            print(f"إجمالي الأفراد: {stats['total_individuals']}")
            print(f"الأطفال: {stats['children_count']}")
            print(f"البالغون: {stats['adults_count']}")
            print(f"متوسط حجم الأسرة: {stats['average_family_size']}")
            
        except Exception as e:
            print(f"❌ خطأ في عرض الإحصائيات: {e}")
    
    def show_families(self):
        """عرض الأسر"""
        print("\n--- قائمة الأسر ---")
        
        try:
            families = self.family_service.get_all_families()
            
            if not families:
                print("لا توجد أسر مسجلة")
                return
            
            print(f"{'رقم الأسرة':<12} {'اسم رب الأسرة':<20} {'عدد الأفراد':<12} {'رقم الخيمة':<12}")
            print("-" * 60)
            
            for family in families:
                print(f"{family.family_id:<12} {family.head_of_family_name:<20} {family.get_total_members():<12} {family.tent_number or 'غير محدد':<12}")
            
        except Exception as e:
            print(f"❌ خطأ في عرض الأسر: {e}")
    
    def search_families(self):
        """البحث عن أسرة"""
        print("\n--- البحث عن أسرة ---")
        
        search_term = input("أدخل اسم رب الأسرة أو رقم الأسرة: ").strip()
        
        if not search_term:
            print("❌ يرجى إدخال كلمة البحث")
            return
        
        try:
            families = self.family_service.search_families(search_term)
            
            if not families:
                print("لم يتم العثور على نتائج")
                return
            
            print(f"تم العثور على {len(families)} نتيجة:")
            print()
            
            for family in families:
                print(f"رقم الأسرة: {family.family_id}")
                print(f"اسم رب الأسرة: {family.head_of_family_name}")
                print(f"رقم الهاتف: {family.phone_number or 'غير محدد'}")
                print(f"رقم الخيمة: {family.tent_number or 'غير محدد'}")
                print(f"عدد الأفراد: {family.get_total_members()}")
                print("-" * 30)
            
        except Exception as e:
            print(f"❌ خطأ في البحث: {e}")
    
    def add_family(self):
        """إضافة أسرة جديدة"""
        print("\n--- إضافة أسرة جديدة ---")
        
        try:
            # جمع بيانات الأسرة
            family_data = {}
            
            family_data['head_of_family_name'] = input("اسم رب الأسرة: ").strip()
            if not family_data['head_of_family_name']:
                print("❌ اسم رب الأسرة مطلوب")
                return
            
            family_data['head_gender'] = input("الجنس (ذكر/أنثى): ").strip()
            family_data['head_marital_status'] = input("الحالة الاجتماعية: ").strip()
            family_data['phone_number'] = input("رقم الهاتف: ").strip()
            family_data['tent_number'] = input("رقم الخيمة: ").strip()
            family_data['nationality'] = input("الجنسية: ").strip()
            family_data['origin_country'] = input("بلد المنشأ: ").strip()
            family_data['origin_city'] = input("مدينة المنشأ: ").strip()
            
            # تاريخ الوصول (افتراضي اليوم)
            family_data['arrival_date'] = date.today()
            
            # إنشاء الأسرة
            success, message, family = self.family_service.create_family(family_data)
            
            if success:
                print(f"✓ {message}")
            else:
                print(f"❌ {message}")
            
        except Exception as e:
            print(f"❌ خطأ في إضافة الأسرة: {e}")
    
    def export_to_excel(self):
        """تصدير البيانات إلى Excel"""
        print("\n--- تصدير البيانات إلى Excel ---")
        
        try:
            families = self.family_service.get_all_families()
            
            if not families:
                print("لا توجد بيانات للتصدير")
                return
            
            filename = f"families_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            
            success = self.excel_service.export_families_to_excel(families, filename)
            
            if success:
                print(f"✓ تم تصدير البيانات إلى: {filename}")
            else:
                print("❌ فشل في تصدير البيانات")
            
        except Exception as e:
            print(f"❌ خطأ في التصدير: {e}")
    
    def create_excel_template(self):
        """إنشاء قالب Excel"""
        print("\n--- إنشاء قالب Excel ---")
        
        try:
            filename = f"families_template_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            
            success = self.excel_service.create_families_template(filename)
            
            if success:
                print(f"✓ تم إنشاء القالب: {filename}")
            else:
                print("❌ فشل في إنشاء القالب")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء القالب: {e}")
    
    def change_password(self):
        """تغيير كلمة المرور"""
        print("\n--- تغيير كلمة المرور ---")
        
        old_password = input("كلمة المرور الحالية: ").strip()
        new_password = input("كلمة المرور الجديدة: ").strip()
        confirm_password = input("تأكيد كلمة المرور الجديدة: ").strip()
        
        if new_password != confirm_password:
            print("❌ كلمة المرور الجديدة غير متطابقة")
            return
        
        success, message = self.auth_service.change_password(old_password, new_password)
        
        if success:
            print(f"✓ {message}")
        else:
            print(f"❌ {message}")
    
    def run(self):
        """تشغيل التطبيق"""
        if not self.setup():
            return 1
        
        print("\n" + "=" * 50)
        print("بيانات المدير الافتراضي:")
        print("اسم المستخدم: admin")
        print("كلمة المرور: admin123")
        print("=" * 50)
        
        if self.login():
            self.show_main_menu()
            return 0
        else:
            return 1

def main():
    """الدالة الرئيسية"""
    app = CampManagementConsole()
    exit_code = app.run()
    
    input("\nاضغط Enter للخروج...")
    sys.exit(exit_code)

if __name__ == "__main__":
    main()
