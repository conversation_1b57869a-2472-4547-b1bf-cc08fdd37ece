"""
تطبيق إدارة المخيمات - نقطة البداية الرئيسية
Camp Management Application - Main Entry Point

الميزات المتاحة:
✅ نظام تسجيل الدخول مع صلاحيات متعددة
✅ إدارة شاملة للأسر والأفراد
✅ استيراد وتصدير Excel
✅ إدارة الأعمدة والجداول المخصصة
✅ نظام توزيع المساعدات
✅ النسخ الاحتياطي والاستعادة
✅ تقارير وإحصائيات تفصيلية
✅ واجهة عربية كاملة مع دعم RTL
"""
import sys
import os
import tkinter as tk
from tkinter import messagebox

# إضافة مسار المشروع إلى sys.path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def check_requirements():
    """التحقق من المتطلبات"""
    required_modules = [
        'sqlalchemy',
        'openpyxl', 
        'bcrypt',
        'tkinter'
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        messagebox.showerror("مكتبات مفقودة", 
                           f"المكتبات التالية مطلوبة ولكنها غير مثبتة:\n"
                           f"{', '.join(missing_modules)}\n\n"
                           "يرجى تثبيتها باستخدام:\n"
                           f"pip install {' '.join(missing_modules)}")
        return False
    return True

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    directories = [
        "data",
        "backups", 
        "templates",
        "exports"
    ]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"تم إنشاء مجلد: {directory}")

def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    try:
        print("🚀 بدء تشغيل تطبيق إدارة المخيمات...")
        print("=" * 50)
        
        # التحقق من المتطلبات
        if not check_requirements():
            return
        
        # إنشاء المجلدات المطلوبة
        create_directories()
        
        # إنشاء النافذة الرئيسية
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة الرئيسية مؤقتاً
        
        print("✅ تم تحميل المكتبات بنجاح")
        print("🔐 فتح نافذة تسجيل الدخول...")
        
        # تشغيل نافذة تسجيل الدخول
        from src.gui_tkinter.login_window import LoginWindow
        login_app = LoginWindow(root)
        
        print("🎯 التطبيق جاهز للاستخدام!")
        print("=" * 50)
        
        # تشغيل التطبيق
        root.mainloop()
        
    except ImportError as e:
        error_msg = f"""خطأ في استيراد المكتبات:
{str(e)}

المكتبات المطلوبة:
• sqlalchemy (قاعدة البيانات)
• openpyxl (ملفات Excel)
• bcrypt (تشفير كلمات المرور)
• tkinter (الواجهة الرسومية)

لتثبيت المكتبات:
pip install sqlalchemy openpyxl bcrypt

أو استخدم:
pip install -r requirements.txt"""
        
        messagebox.showerror("خطأ في الاستيراد", error_msg)
        print("❌ فشل في تحميل المكتبات المطلوبة")
        
    except Exception as e:
        error_msg = f"""حدث خطأ غير متوقع:
{str(e)}

يرجى التأكد من:
• وجود ملفات التطبيق كاملة
• صلاحيات الكتابة في مجلد التطبيق
• عدم تشغيل نسخة أخرى من التطبيق"""
        
        messagebox.showerror("خطأ", error_msg)
        print(f"❌ خطأ غير متوقع: {str(e)}")

if __name__ == "__main__":
    main()
