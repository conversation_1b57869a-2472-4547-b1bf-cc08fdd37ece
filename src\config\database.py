"""
إعداد قاعدة البيانات
Database Configuration
"""
from sqlalchemy import create_engine, event
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.engine import Engine
import sqlite3
import os
from .settings import DATABASE_URL, DATABASE_DIR

# إنشاء قاعدة البيانات الأساسية
Base = declarative_base()

# إعداد المحرك
engine = None
SessionLocal = None

def init_database():
    """تهيئة قاعدة البيانات"""
    global engine, SessionLocal
    
    # إنشاء مجلد قاعدة البيانات إذا لم يكن موجوداً
    os.makedirs(DATABASE_DIR, exist_ok=True)
    
    # إنشاء المحرك
    engine = create_engine(
        DATABASE_URL,
        connect_args={"check_same_thread": False},
        echo=False  # تغيير إلى True لرؤية استعلامات SQL
    )
    
    # تفعيل Foreign Keys في SQLite
    @event.listens_for(Engine, "connect")
    def set_sqlite_pragma(dbapi_connection, connection_record):
        if isinstance(dbapi_connection, sqlite3.Connection):
            cursor = dbapi_connection.cursor()
            cursor.execute("PRAGMA foreign_keys=ON")
            cursor.close()
    
    # إنشاء جلسة قاعدة البيانات
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    
    return engine

def create_tables():
    """إنشاء جداول قاعدة البيانات"""
    if engine is None:
        init_database()
    
    # استيراد جميع النماذج لضمان إنشاء الجداول
    from ..models import user, family, individual, aid_distribution
    
    # إنشاء الجداول
    Base.metadata.create_all(bind=engine)
    print("تم إنشاء جداول قاعدة البيانات بنجاح")

def get_db():
    """الحصول على جلسة قاعدة البيانات"""
    if SessionLocal is None:
        init_database()
    
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def get_db_session():
    """الحصول على جلسة قاعدة البيانات للاستخدام المباشر"""
    if SessionLocal is None:
        init_database()
    return SessionLocal()

# تهيئة قاعدة البيانات عند استيراد الوحدة
init_database()
