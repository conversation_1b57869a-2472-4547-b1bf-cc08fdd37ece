"""
إعدادات التطبيق الرئيسية
Application Settings
"""
import os
from pathlib import Path

# مسارات التطبيق
BASE_DIR = Path(__file__).parent.parent.parent
DATABASE_DIR = BASE_DIR / "database"
RESOURCES_DIR = BASE_DIR / "resources"
TEMPLATES_DIR = RESOURCES_DIR / "templates"
ICONS_DIR = RESOURCES_DIR / "icons"

# إعدادات قاعدة البيانات
DATABASE_URL = f"sqlite:///{DATABASE_DIR}/camp.db"

# إعدادات الأمان
SECRET_KEY = "camp_management_secret_key_2024"
PASSWORD_SALT_ROUNDS = 12

# إعدادات الواجهة
WINDOW_TITLE = "نظام إدارة المخيم - Camp Management System"
WINDOW_MIN_WIDTH = 1200
WINDOW_MIN_HEIGHT = 800

# إعدادات اللغة والنص
DEFAULT_LANGUAGE = "ar"
RTL_SUPPORT = True

# إعدادات Excel
EXCEL_TEMPLATE_FAMILIES = "families_template.xlsx"
EXCEL_EXPORT_FAMILIES = "families_export.xlsx"

# أدوار المستخدمين
USER_ROLES = {
    "admin": "مدير",
    "data_entry": "مدخل بيانات",
    "distribution_officer": "مسؤول توزيع",
    "guest": "زائر"
}

# نظام الصلاحيات المتقدم
ROLE_PERMISSIONS = {
    "admin": {
        "families": ["create", "read", "update", "delete", "export", "import"],
        "users": ["create", "read", "update", "delete"],
        "reports": ["view_all", "create_custom", "export"],
        "aid_distribution": ["create", "read", "update", "delete", "reports"],
        "system": ["backup", "restore", "settings"]
    },
    "data_entry": {
        "families": ["create", "read", "update"],
        "users": [],
        "reports": ["view_basic"],
        "aid_distribution": [],
        "system": []
    },
    "distribution_officer": {
        "families": ["read"],
        "users": [],
        "reports": ["view_distribution"],
        "aid_distribution": ["create", "read", "update", "reports"],
        "system": []
    },
    "guest": {
        "families": ["read"],
        "users": [],
        "reports": ["view_basic"],
        "aid_distribution": ["read"],
        "system": []
    }
}

# أوصاف الأدوار
ROLE_DESCRIPTIONS = {
    "admin": "مدير النظام - وصول كامل لجميع الوظائف",
    "data_entry": "مدخل بيانات - إدارة بيانات الأسر والأفراد",
    "distribution_officer": "مسؤول التوزيع - إدارة توزيع المساعدات",
    "guest": "زائر - عرض البيانات فقط"
}

# إعدادات التقارير
REPORT_FORMATS = ["excel", "pdf"]
DEFAULT_REPORT_FORMAT = "excel"

# إنشاء المجلدات المطلوبة
def create_directories():
    """إنشاء المجلدات المطلوبة للتطبيق"""
    directories = [DATABASE_DIR, RESOURCES_DIR, TEMPLATES_DIR, ICONS_DIR]
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)

if __name__ == "__main__":
    create_directories()
    print("تم إنشاء المجلدات المطلوبة بنجاح")
