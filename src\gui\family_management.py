"""
وحدة إدارة الأسر
Family Management Widget
"""
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
                            QTableWidgetItem, QPushButton, QLineEdit, QLabel,
                            QMessageBox, QDialog, QFormLayout, QDateEdit,
                            QComboBox, QTextEdit, QHeaderView, QFileDialog,
                            QProgressDialog, QFrame, QGroupBox, QGridLayout,
                            QSpinBox, QScrollArea)
from PyQt5.QtCore import Qt, QDate, QThread, pyqtSignal
from PyQt5.QtGui import QFont
from datetime import datetime, date
from typing import List, Dict, Any
from ..services.family_service import FamilyService
from ..services.excel_service import ExcelService
from ..models.family import Family

class FamilyManagementWidget(QWidget):
    """وحدة إدارة الأسر"""

    def __init__(self, auth_service):
        super().__init__()
        self.auth_service = auth_service
        self.family_service = FamilyService()
        self.excel_service = ExcelService()
        self.families_data = []

        self.setup_ui()
        self.load_families()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)

        # شريط الأدوات العلوي
        toolbar_frame = QFrame()
        toolbar_layout = QHBoxLayout(toolbar_frame)

        # البحث
        search_label = QLabel("البحث:")
        toolbar_layout.addWidget(search_label)

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("ابحث بالاسم، رقم الأسرة، رقم الهاتف...")
        self.search_input.textChanged.connect(self.search_families)
        toolbar_layout.addWidget(self.search_input)

        toolbar_layout.addStretch()

        # أزرار العمليات
        if self.auth_service.has_permission("create"):
            self.add_button = QPushButton("إضافة أسرة")
            self.add_button.clicked.connect(self.show_add_family_dialog)
            toolbar_layout.addWidget(self.add_button)

        if self.auth_service.has_permission("update"):
            self.edit_button = QPushButton("تعديل")
            self.edit_button.clicked.connect(self.edit_selected_family)
            self.edit_button.setEnabled(False)
            toolbar_layout.addWidget(self.edit_button)

        if self.auth_service.has_permission("delete"):
            self.archive_button = QPushButton("أرشفة")
            self.archive_button.clicked.connect(self.archive_selected_family)
            self.archive_button.setEnabled(False)
            toolbar_layout.addWidget(self.archive_button)

        # أزرار Excel
        self.import_button = QPushButton("استيراد Excel")
        self.import_button.clicked.connect(self.import_from_excel)
        toolbar_layout.addWidget(self.import_button)

        self.export_button = QPushButton("تصدير Excel")
        self.export_button.clicked.connect(self.export_to_excel)
        toolbar_layout.addWidget(self.export_button)

        self.template_button = QPushButton("تحميل قالب")
        self.template_button.clicked.connect(self.download_template)
        toolbar_layout.addWidget(self.template_button)

        self.refresh_button = QPushButton("تحديث")
        self.refresh_button.clicked.connect(self.load_families)
        toolbar_layout.addWidget(self.refresh_button)

        layout.addWidget(toolbar_frame)

        # جدول الأسر
        self.families_table = QTableWidget()
        self.setup_table()
        layout.addWidget(self.families_table)

        # شريط المعلومات السفلي
        info_frame = QFrame()
        info_layout = QHBoxLayout(info_frame)

        self.info_label = QLabel("إجمالي الأسر: 0")
        info_layout.addWidget(self.info_label)

        info_layout.addStretch()

        self.status_label = QLabel("جاهز")
        info_layout.addWidget(self.status_label)

        layout.addWidget(info_frame)

        # تطبيق الأنماط
        self.setup_styles()

    def setup_table(self):
        """إعداد جدول الأسر"""
        headers = [
            "رقم الأسرة", "اسم رب الأسرة", "الجنس", "رقم الهاتف",
            "تاريخ الوصول", "رقم الخيمة", "عدد الأفراد", "الجنسية", "ملاحظات"
        ]

        self.families_table.setColumnCount(len(headers))
        self.families_table.setHorizontalHeaderLabels(headers)

        # إعداد خصائص الجدول
        self.families_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.families_table.setSelectionMode(QTableWidget.SingleSelection)
        self.families_table.setAlternatingRowColors(True)
        self.families_table.setSortingEnabled(True)

        # تعديل عرض الأعمدة
        header = self.families_table.horizontalHeader()
        header.setStretchLastSection(True)
        for i in range(len(headers) - 1):
            header.setSectionResizeMode(i, QHeaderView.ResizeToContents)

        # ربط إشارة التحديد
        self.families_table.selectionModel().selectionChanged.connect(self.on_selection_changed)

        # ربط النقر المزدوج
        self.families_table.doubleClicked.connect(self.view_family_details)

    def setup_styles(self):
        """إعداد الأنماط"""
        self.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 5px;
                padding: 5px;
                margin: 2px;
            }
            QTableWidget {
                gridline-color: #ddd;
                background-color: white;
                alternate-background-color: #f9f9f9;
            }
            QTableWidget::item {
                padding: 8px;
            }
            QTableWidget::item:selected {
                background-color: #366092;
                color: white;
            }
            QHeaderView::section {
                background-color: #366092;
                color: white;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
            QPushButton {
                background-color: #366092;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #2d4f7a;
            }
            QPushButton:disabled {
                background-color: #ccc;
                color: #666;
            }
            QLineEdit {
                border: 2px solid #ddd;
                border-radius: 4px;
                padding: 6px;
                font-size: 11px;
            }
            QLineEdit:focus {
                border-color: #366092;
            }
        """)

    def load_families(self):
        """تحميل بيانات الأسر"""
        try:
            self.status_label.setText("جاري تحميل البيانات...")
            self.families_data = self.family_service.get_all_families()
            self.populate_table(self.families_data)
            self.update_info_label()
            self.status_label.setText("تم تحميل البيانات بنجاح")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل البيانات: {str(e)}")
            self.status_label.setText("فشل في تحميل البيانات")

    def populate_table(self, families: List[Family]):
        """ملء الجدول بالبيانات"""
        self.families_table.setRowCount(len(families))

        for row, family in enumerate(families):
            self.families_table.setItem(row, 0, QTableWidgetItem(family.family_id))
            self.families_table.setItem(row, 1, QTableWidgetItem(family.head_of_family_name))
            self.families_table.setItem(row, 2, QTableWidgetItem(family.head_gender or ""))
            self.families_table.setItem(row, 3, QTableWidgetItem(family.phone_number or ""))

            arrival_date = family.arrival_date.strftime('%Y-%m-%d') if family.arrival_date else ""
            self.families_table.setItem(row, 4, QTableWidgetItem(arrival_date))

            self.families_table.setItem(row, 5, QTableWidgetItem(family.tent_number or ""))
            self.families_table.setItem(row, 6, QTableWidgetItem(str(family.get_total_members())))
            self.families_table.setItem(row, 7, QTableWidgetItem(family.nationality or ""))
            self.families_table.setItem(row, 8, QTableWidgetItem(family.notes or ""))

            # حفظ مرجع للأسرة في الصف
            self.families_table.item(row, 0).setData(Qt.UserRole, family)

    def search_families(self):
        """البحث في الأسر"""
        search_term = self.search_input.text().strip()

        if not search_term:
            self.populate_table(self.families_data)
            return

        try:
            filtered_families = self.family_service.search_families(search_term)
            self.populate_table(filtered_families)
            self.update_info_label(len(filtered_families))

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في البحث: {str(e)}")

    def update_info_label(self, count=None):
        """تحديث تسمية المعلومات"""
        if count is None:
            count = len(self.families_data)
        self.info_label.setText(f"إجمالي الأسر: {count}")

    def on_selection_changed(self):
        """عند تغيير التحديد في الجدول"""
        has_selection = len(self.families_table.selectedItems()) > 0

        if hasattr(self, 'edit_button'):
            self.edit_button.setEnabled(has_selection)
        if hasattr(self, 'archive_button'):
            self.archive_button.setEnabled(has_selection)

    def get_selected_family(self) -> Family:
        """الحصول على الأسرة المحددة"""
        current_row = self.families_table.currentRow()
        if current_row >= 0:
            item = self.families_table.item(current_row, 0)
            if item:
                return item.data(Qt.UserRole)
        return None

    def show_add_family_dialog(self):
        """إظهار حوار إضافة أسرة جديدة"""
        dialog = AddFamilyDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_families()

    def edit_selected_family(self):
        """تعديل الأسرة المحددة"""
        family = self.get_selected_family()
        if family:
            dialog = EditFamilyDialog(family, self)
            if dialog.exec_() == QDialog.Accepted:
                self.load_families()

    def archive_selected_family(self):
        """أرشفة الأسرة المحددة"""
        family = self.get_selected_family()
        if family:
            reply = QMessageBox.question(
                self, "تأكيد الأرشفة",
                f"هل أنت متأكد من أرشفة الأسرة: {family.head_of_family_name}؟\n"
                "سيتم نقل الأسرة إلى الأرشيف ولن تظهر في القائمة الرئيسية.",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                success, message = self.family_service.archive_family(family.family_id)
                if success:
                    QMessageBox.information(self, "نجح", message)
                    self.load_families()
                else:
                    QMessageBox.warning(self, "خطأ", message)

    def view_family_details(self):
        """عرض تفاصيل الأسرة"""
        family = self.get_selected_family()
        if family:
            dialog = FamilyDetailsDialog(family, self)
            dialog.exec_()

    def import_from_excel(self):
        """استيراد البيانات من Excel"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "اختر ملف Excel", "", "Excel Files (*.xlsx *.xls)"
        )

        if file_path:
            try:
                success, message, families_data, individuals_data = self.excel_service.import_families_from_excel(file_path)

                if success:
                    # إظهار حوار تأكيد الاستيراد
                    dialog = ImportConfirmDialog(families_data, individuals_data, self)
                    if dialog.exec_() == QDialog.Accepted:
                        self.load_families()
                else:
                    QMessageBox.warning(self, "خطأ", message)

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في استيراد الملف: {str(e)}")

    def export_to_excel(self):
        """تصدير البيانات إلى Excel"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "حفظ ملف Excel", "families_export.xlsx", "Excel Files (*.xlsx)"
        )

        if file_path:
            try:
                families_to_export = self.families_data
                if self.search_input.text().strip():
                    # تصدير نتائج البحث فقط
                    families_to_export = self.family_service.search_families(self.search_input.text().strip())

                success = self.excel_service.export_families_to_excel(families_to_export, file_path)

                if success:
                    QMessageBox.information(self, "نجح", f"تم تصدير البيانات بنجاح إلى:\n{file_path}")
                else:
                    QMessageBox.warning(self, "خطأ", "فشل في تصدير البيانات")

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في تصدير البيانات: {str(e)}")

    def download_template(self):
        """تحميل قالب Excel"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "حفظ قالب Excel", "families_template.xlsx", "Excel Files (*.xlsx)"
        )

        if file_path:
            try:
                success = self.excel_service.create_families_template(file_path)

                if success:
                    QMessageBox.information(self, "نجح", f"تم إنشاء القالب بنجاح:\n{file_path}")
                else:
                    QMessageBox.warning(self, "خطأ", "فشل في إنشاء القالب")

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في إنشاء القالب: {str(e)}")


class AddFamilyDialog(QDialog):
    """حوار إضافة أسرة جديدة"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.family_service = FamilyService()
        self.individuals_data = []
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("إضافة أسرة جديدة")
        self.setModal(True)
        self.resize(600, 700)

        layout = QVBoxLayout(self)

        # إنشاء منطقة تمرير
        scroll = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)

        # معلومات رب الأسرة
        head_group = QGroupBox("معلومات رب الأسرة")
        head_layout = QFormLayout(head_group)

        self.head_name = QLineEdit()
        self.head_name.setPlaceholderText("الاسم الكامل")
        head_layout.addRow("الاسم:", self.head_name)

        self.head_birth_date = QDateEdit()
        self.head_birth_date.setDate(QDate.currentDate().addYears(-30))
        self.head_birth_date.setCalendarPopup(True)
        head_layout.addRow("تاريخ الميلاد:", self.head_birth_date)

        self.head_gender = QComboBox()
        self.head_gender.addItems(["ذكر", "أنثى"])
        head_layout.addRow("الجنس:", self.head_gender)

        self.head_marital_status = QComboBox()
        self.head_marital_status.addItems(["أعزب", "متزوج", "مطلق", "أرمل"])
        head_layout.addRow("الحالة الاجتماعية:", self.head_marital_status)

        self.phone_number = QLineEdit()
        self.phone_number.setPlaceholderText("+970123456789")
        head_layout.addRow("رقم الهاتف:", self.phone_number)

        self.identity_document = QLineEdit()
        head_layout.addRow("وثيقة الهوية:", self.identity_document)

        scroll_layout.addWidget(head_group)

        # معلومات السكن
        housing_group = QGroupBox("معلومات السكن")
        housing_layout = QFormLayout(housing_group)

        self.arrival_date = QDateEdit()
        self.arrival_date.setDate(QDate.currentDate())
        self.arrival_date.setCalendarPopup(True)
        housing_layout.addRow("تاريخ الوصول:", self.arrival_date)

        self.tent_number = QLineEdit()
        housing_layout.addRow("رقم الخيمة/المأوى:", self.tent_number)

        self.shelter_type = QComboBox()
        self.shelter_type.addItems(["خيمة", "كرفان", "غرفة", "أخرى"])
        housing_layout.addRow("نوع المأوى:", self.shelter_type)

        scroll_layout.addWidget(housing_group)

        # معلومات إضافية
        additional_group = QGroupBox("معلومات إضافية")
        additional_layout = QFormLayout(additional_group)

        self.nationality = QLineEdit()
        additional_layout.addRow("الجنسية:", self.nationality)

        self.origin_country = QLineEdit()
        additional_layout.addRow("بلد المنشأ:", self.origin_country)

        self.origin_city = QLineEdit()
        additional_layout.addRow("مدينة المنشأ:", self.origin_city)

        self.special_needs = QTextEdit()
        self.special_needs.setMaximumHeight(60)
        additional_layout.addRow("احتياجات خاصة:", self.special_needs)

        self.medical_conditions = QTextEdit()
        self.medical_conditions.setMaximumHeight(60)
        additional_layout.addRow("حالات طبية:", self.medical_conditions)

        self.notes = QTextEdit()
        self.notes.setMaximumHeight(60)
        additional_layout.addRow("ملاحظات:", self.notes)

        scroll_layout.addWidget(additional_group)

        scroll.setWidget(scroll_widget)
        layout.addWidget(scroll)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        save_button = QPushButton("حفظ")
        save_button.clicked.connect(self.save_family)
        buttons_layout.addWidget(save_button)

        cancel_button = QPushButton("إلغاء")
        cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_button)

        layout.addLayout(buttons_layout)

    def save_family(self):
        """حفظ الأسرة الجديدة"""
        # التحقق من البيانات المطلوبة
        if not self.head_name.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم رب الأسرة")
            return

        try:
            family_data = {
                'head_of_family_name': self.head_name.text().strip(),
                'head_birth_date': self.head_birth_date.date().toPyDate(),
                'head_gender': self.head_gender.currentText(),
                'head_marital_status': self.head_marital_status.currentText(),
                'phone_number': self.phone_number.text().strip(),
                'identity_document': self.identity_document.text().strip(),
                'arrival_date': self.arrival_date.date().toPyDate(),
                'tent_number': self.tent_number.text().strip(),
                'shelter_type': self.shelter_type.currentText(),
                'nationality': self.nationality.text().strip(),
                'origin_country': self.origin_country.text().strip(),
                'origin_city': self.origin_city.text().strip(),
                'special_needs': self.special_needs.toPlainText().strip(),
                'medical_conditions': self.medical_conditions.toPlainText().strip(),
                'notes': self.notes.toPlainText().strip()
            }

            success, message, family = self.family_service.create_family(family_data)

            if success:
                QMessageBox.information(self, "نجح", message)
                self.accept()
            else:
                QMessageBox.warning(self, "خطأ", message)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الأسرة: {str(e)}")


class EditFamilyDialog(QDialog):
    """حوار تعديل أسرة موجودة"""

    def __init__(self, family, parent=None):
        super().__init__(parent)
        self.family = family
        self.family_service = FamilyService()
        self.setup_ui()
        self.load_family_data()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle(f"تعديل الأسرة - {self.family.family_id}")
        self.setModal(True)
        self.resize(600, 700)

        # نفس التخطيط كما في AddFamilyDialog
        # (يمكن تحسين هذا بإنشاء فئة أساسية مشتركة)
        layout = QVBoxLayout(self)

        scroll = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)

        # معلومات رب الأسرة
        head_group = QGroupBox("معلومات رب الأسرة")
        head_layout = QFormLayout(head_group)

        self.head_name = QLineEdit()
        head_layout.addRow("الاسم:", self.head_name)

        self.head_birth_date = QDateEdit()
        self.head_birth_date.setCalendarPopup(True)
        head_layout.addRow("تاريخ الميلاد:", self.head_birth_date)

        self.head_gender = QComboBox()
        self.head_gender.addItems(["ذكر", "أنثى"])
        head_layout.addRow("الجنس:", self.head_gender)

        self.head_marital_status = QComboBox()
        self.head_marital_status.addItems(["أعزب", "متزوج", "مطلق", "أرمل"])
        head_layout.addRow("الحالة الاجتماعية:", self.head_marital_status)

        self.phone_number = QLineEdit()
        head_layout.addRow("رقم الهاتف:", self.phone_number)

        self.identity_document = QLineEdit()
        head_layout.addRow("وثيقة الهوية:", self.identity_document)

        scroll_layout.addWidget(head_group)

        # معلومات السكن
        housing_group = QGroupBox("معلومات السكن")
        housing_layout = QFormLayout(housing_group)

        self.arrival_date = QDateEdit()
        self.arrival_date.setCalendarPopup(True)
        housing_layout.addRow("تاريخ الوصول:", self.arrival_date)

        self.tent_number = QLineEdit()
        housing_layout.addRow("رقم الخيمة/المأوى:", self.tent_number)

        self.shelter_type = QComboBox()
        self.shelter_type.addItems(["خيمة", "كرفان", "غرفة", "أخرى"])
        housing_layout.addRow("نوع المأوى:", self.shelter_type)

        scroll_layout.addWidget(housing_group)

        # معلومات إضافية
        additional_group = QGroupBox("معلومات إضافية")
        additional_layout = QFormLayout(additional_group)

        self.nationality = QLineEdit()
        additional_layout.addRow("الجنسية:", self.nationality)

        self.origin_country = QLineEdit()
        additional_layout.addRow("بلد المنشأ:", self.origin_country)

        self.origin_city = QLineEdit()
        additional_layout.addRow("مدينة المنشأ:", self.origin_city)

        self.special_needs = QTextEdit()
        self.special_needs.setMaximumHeight(60)
        additional_layout.addRow("احتياجات خاصة:", self.special_needs)

        self.medical_conditions = QTextEdit()
        self.medical_conditions.setMaximumHeight(60)
        additional_layout.addRow("حالات طبية:", self.medical_conditions)

        self.notes = QTextEdit()
        self.notes.setMaximumHeight(60)
        additional_layout.addRow("ملاحظات:", self.notes)

        scroll_layout.addWidget(additional_group)

        scroll.setWidget(scroll_widget)
        layout.addWidget(scroll)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        save_button = QPushButton("حفظ التغييرات")
        save_button.clicked.connect(self.save_changes)
        buttons_layout.addWidget(save_button)

        cancel_button = QPushButton("إلغاء")
        cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_button)

        layout.addLayout(buttons_layout)

    def load_family_data(self):
        """تحميل بيانات الأسرة الحالية"""
        self.head_name.setText(self.family.head_of_family_name or "")

        if self.family.head_birth_date:
            self.head_birth_date.setDate(QDate(self.family.head_birth_date))

        if self.family.head_gender:
            index = self.head_gender.findText(self.family.head_gender)
            if index >= 0:
                self.head_gender.setCurrentIndex(index)

        if self.family.head_marital_status:
            index = self.head_marital_status.findText(self.family.head_marital_status)
            if index >= 0:
                self.head_marital_status.setCurrentIndex(index)

        self.phone_number.setText(self.family.phone_number or "")
        self.identity_document.setText(self.family.identity_document or "")

        if self.family.arrival_date:
            self.arrival_date.setDate(QDate(self.family.arrival_date))

        self.tent_number.setText(self.family.tent_number or "")

        if self.family.shelter_type:
            index = self.shelter_type.findText(self.family.shelter_type)
            if index >= 0:
                self.shelter_type.setCurrentIndex(index)

        self.nationality.setText(self.family.nationality or "")
        self.origin_country.setText(self.family.origin_country or "")
        self.origin_city.setText(self.family.origin_city or "")
        self.special_needs.setPlainText(self.family.special_needs or "")
        self.medical_conditions.setPlainText(self.family.medical_conditions or "")
        self.notes.setPlainText(self.family.notes or "")

    def save_changes(self):
        """حفظ التغييرات"""
        if not self.head_name.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم رب الأسرة")
            return

        try:
            family_data = {
                'head_of_family_name': self.head_name.text().strip(),
                'head_birth_date': self.head_birth_date.date().toPyDate(),
                'head_gender': self.head_gender.currentText(),
                'head_marital_status': self.head_marital_status.currentText(),
                'phone_number': self.phone_number.text().strip(),
                'identity_document': self.identity_document.text().strip(),
                'arrival_date': self.arrival_date.date().toPyDate(),
                'tent_number': self.tent_number.text().strip(),
                'shelter_type': self.shelter_type.currentText(),
                'nationality': self.nationality.text().strip(),
                'origin_country': self.origin_country.text().strip(),
                'origin_city': self.origin_city.text().strip(),
                'special_needs': self.special_needs.toPlainText().strip(),
                'medical_conditions': self.medical_conditions.toPlainText().strip(),
                'notes': self.notes.toPlainText().strip()
            }

            success, message = self.family_service.update_family(self.family.family_id, family_data)

            if success:
                QMessageBox.information(self, "نجح", message)
                self.accept()
            else:
                QMessageBox.warning(self, "خطأ", message)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ التغييرات: {str(e)}")


class FamilyDetailsDialog(QDialog):
    """حوار عرض تفاصيل الأسرة"""

    def __init__(self, family, parent=None):
        super().__init__(parent)
        self.family = family
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle(f"تفاصيل الأسرة - {self.family.family_id}")
        self.setModal(True)
        self.resize(700, 600)

        layout = QVBoxLayout(self)

        # عنوان
        title = QLabel(f"تفاصيل الأسرة: {self.family.head_of_family_name}")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)

        # منطقة تمرير
        scroll = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)

        # معلومات الأسرة
        family_info = self.create_family_info_widget()
        scroll_layout.addWidget(family_info)

        # أفراد الأسرة
        if self.family.individuals:
            individuals_info = self.create_individuals_info_widget()
            scroll_layout.addWidget(individuals_info)

        scroll.setWidget(scroll_widget)
        layout.addWidget(scroll)

        # زر الإغلاق
        close_button = QPushButton("إغلاق")
        close_button.clicked.connect(self.accept)
        layout.addWidget(close_button)

    def create_family_info_widget(self):
        """إنشاء ويدجت معلومات الأسرة"""
        group = QGroupBox("معلومات رب الأسرة")
        layout = QFormLayout(group)

        layout.addRow("رقم الأسرة:", QLabel(self.family.family_id))
        layout.addRow("الاسم:", QLabel(self.family.head_of_family_name))

        if self.family.head_birth_date:
            layout.addRow("تاريخ الميلاد:", QLabel(self.family.head_birth_date.strftime('%Y-%m-%d')))

        layout.addRow("الجنس:", QLabel(self.family.head_gender or ""))
        layout.addRow("الحالة الاجتماعية:", QLabel(self.family.head_marital_status or ""))
        layout.addRow("رقم الهاتف:", QLabel(self.family.phone_number or ""))
        layout.addRow("وثيقة الهوية:", QLabel(self.family.identity_document or ""))

        if self.family.arrival_date:
            layout.addRow("تاريخ الوصول:", QLabel(self.family.arrival_date.strftime('%Y-%m-%d')))

        layout.addRow("رقم الخيمة:", QLabel(self.family.tent_number or ""))
        layout.addRow("نوع المأوى:", QLabel(self.family.shelter_type or ""))
        layout.addRow("عدد الأفراد:", QLabel(str(self.family.get_total_members())))
        layout.addRow("الجنسية:", QLabel(self.family.nationality or ""))
        layout.addRow("بلد المنشأ:", QLabel(self.family.origin_country or ""))
        layout.addRow("مدينة المنشأ:", QLabel(self.family.origin_city or ""))

        if self.family.special_needs:
            layout.addRow("احتياجات خاصة:", QLabel(self.family.special_needs))

        if self.family.medical_conditions:
            layout.addRow("حالات طبية:", QLabel(self.family.medical_conditions))

        if self.family.notes:
            layout.addRow("ملاحظات:", QLabel(self.family.notes))

        return group

    def create_individuals_info_widget(self):
        """إنشاء ويدجت معلومات أفراد الأسرة"""
        group = QGroupBox(f"أفراد الأسرة ({len(self.family.individuals)} فرد)")
        layout = QVBoxLayout(group)

        for individual in self.family.individuals:
            individual_frame = QFrame()
            individual_frame.setFrameStyle(QFrame.Box)
            individual_layout = QFormLayout(individual_frame)

            individual_layout.addRow("الاسم:", QLabel(individual.full_name))
            individual_layout.addRow("صلة القرابة:", QLabel(individual.relationship_to_head))

            if individual.birth_date:
                age = individual.get_age()
                birth_info = f"{individual.birth_date.strftime('%Y-%m-%d')} ({age} سنة)"
                individual_layout.addRow("تاريخ الميلاد:", QLabel(birth_info))

            individual_layout.addRow("الجنس:", QLabel(individual.gender))

            if individual.education_level:
                individual_layout.addRow("المستوى التعليمي:", QLabel(individual.education_level))

            if individual.occupation:
                individual_layout.addRow("المهنة:", QLabel(individual.occupation))

            layout.addWidget(individual_frame)

        return group


class ImportConfirmDialog(QDialog):
    """حوار تأكيد الاستيراد"""

    def __init__(self, families_data, individuals_data, parent=None):
        super().__init__(parent)
        self.families_data = families_data
        self.individuals_data = individuals_data
        self.family_service = FamilyService()
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("تأكيد استيراد البيانات")
        self.setModal(True)
        self.resize(500, 400)

        layout = QVBoxLayout(self)

        # معلومات الاستيراد
        info_label = QLabel(f"تم العثور على:\n"
                           f"• {len(self.families_data)} أسرة\n"
                           f"• {len(self.individuals_data)} فرد\n\n"
                           f"هل تريد المتابعة مع الاستيراد؟")
        info_label.setFont(QFont("Arial", 12))
        layout.addWidget(info_label)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        import_button = QPushButton("استيراد")
        import_button.clicked.connect(self.import_data)
        buttons_layout.addWidget(import_button)

        cancel_button = QPushButton("إلغاء")
        cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_button)

        layout.addLayout(buttons_layout)

    def import_data(self):
        """تنفيذ عملية الاستيراد"""
        try:
            progress = QProgressDialog("جاري استيراد البيانات...", "إلغاء", 0, len(self.families_data), self)
            progress.setWindowModality(Qt.WindowModal)

            imported_count = 0

            for i, family_data in enumerate(self.families_data):
                if progress.wasCanceled():
                    break

                # استيراد الأسرة
                success, message, family = self.family_service.create_family(family_data)

                if success:
                    imported_count += 1

                progress.setValue(i + 1)

            progress.close()

            QMessageBox.information(self, "اكتمل الاستيراد",
                                  f"تم استيراد {imported_count} أسرة من أصل {len(self.families_data)}")

            self.accept()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في الاستيراد: {str(e)}")
            self.reject()
