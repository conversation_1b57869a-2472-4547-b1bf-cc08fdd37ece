"""
نافذة تسجيل الدخول
Login Window
"""
import sys
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QLabel, QLineEdit, QPushButton, 
                            QMessageBox, QFrame, QCheckBox)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap, QPalette, QColor
from ..services.auth_service import AuthService

class LoginWindow(QMainWindow):
    """نافذة تسجيل الدخول"""
    
    # إشارة عند نجاح تسجيل الدخول
    login_successful = pyqtSignal(object)  # سيرسل كائن المستخدم
    
    def __init__(self):
        super().__init__()
        self.auth_service = AuthService()
        self.setup_ui()
        self.setup_styles()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("تسجيل الدخول - نظام إدارة المخيم")
        self.setFixedSize(400, 500)
        self.setWindowFlags(Qt.WindowCloseButtonHint | Qt.WindowMinimizeButtonHint)
        
        # الويدجت الرئيسي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(40, 40, 40, 40)
        
        # إطار تسجيل الدخول
        login_frame = QFrame()
        login_frame.setFrameStyle(QFrame.Box)
        login_frame.setLineWidth(2)
        login_layout = QVBoxLayout(login_frame)
        login_layout.setSpacing(20)
        login_layout.setContentsMargins(30, 30, 30, 30)
        
        # العنوان
        title_label = QLabel("نظام إدارة المخيم")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Arial", 18, QFont.Bold))
        login_layout.addWidget(title_label)
        
        subtitle_label = QLabel("Camp Management System")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setFont(QFont("Arial", 12))
        login_layout.addWidget(subtitle_label)
        
        # مساحة فارغة
        login_layout.addSpacing(20)
        
        # حقل اسم المستخدم
        username_label = QLabel("اسم المستخدم:")
        username_label.setFont(QFont("Arial", 10, QFont.Bold))
        login_layout.addWidget(username_label)
        
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("أدخل اسم المستخدم")
        self.username_input.setFont(QFont("Arial", 10))
        self.username_input.setMinimumHeight(35)
        login_layout.addWidget(self.username_input)
        
        # حقل كلمة المرور
        password_label = QLabel("كلمة المرور:")
        password_label.setFont(QFont("Arial", 10, QFont.Bold))
        login_layout.addWidget(password_label)
        
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("أدخل كلمة المرور")
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setFont(QFont("Arial", 10))
        self.password_input.setMinimumHeight(35)
        login_layout.addWidget(self.password_input)
        
        # خيار تذكر كلمة المرور
        self.remember_checkbox = QCheckBox("تذكر كلمة المرور")
        self.remember_checkbox.setFont(QFont("Arial", 9))
        login_layout.addWidget(self.remember_checkbox)
        
        # مساحة فارغة
        login_layout.addSpacing(10)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        self.login_button = QPushButton("تسجيل الدخول")
        self.login_button.setFont(QFont("Arial", 11, QFont.Bold))
        self.login_button.setMinimumHeight(40)
        self.login_button.clicked.connect(self.handle_login)
        buttons_layout.addWidget(self.login_button)
        
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.setFont(QFont("Arial", 11))
        self.cancel_button.setMinimumHeight(40)
        self.cancel_button.clicked.connect(self.close)
        buttons_layout.addWidget(self.cancel_button)
        
        login_layout.addLayout(buttons_layout)
        
        # معلومات المدير الافتراضي
        info_label = QLabel("المدير الافتراضي:\nاسم المستخدم: admin\nكلمة المرور: admin123")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setFont(QFont("Arial", 8))
        info_label.setStyleSheet("color: #666; border: 1px solid #ccc; padding: 10px; background-color: #f9f9f9;")
        login_layout.addWidget(info_label)
        
        main_layout.addWidget(login_frame)
        
        # ربط Enter بتسجيل الدخول
        self.username_input.returnPressed.connect(self.handle_login)
        self.password_input.returnPressed.connect(self.handle_login)
        
        # تركيز على حقل اسم المستخدم
        self.username_input.setFocus()
        
    def setup_styles(self):
        """إعداد الأنماط"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f0f0f0;
            }
            QFrame {
                background-color: white;
                border: 2px solid #366092;
                border-radius: 10px;
            }
            QLabel {
                color: #333;
            }
            QLineEdit {
                border: 2px solid #ddd;
                border-radius: 5px;
                padding: 8px;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #366092;
            }
            QPushButton {
                background-color: #366092;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2d4f7a;
            }
            QPushButton:pressed {
                background-color: #1e3a5f;
            }
            QPushButton#cancel_button {
                background-color: #666;
            }
            QPushButton#cancel_button:hover {
                background-color: #555;
            }
            QCheckBox {
                color: #333;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
            QCheckBox::indicator:unchecked {
                border: 2px solid #ddd;
                background-color: white;
                border-radius: 3px;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #366092;
                background-color: #366092;
                border-radius: 3px;
            }
        """)
        
        # تعيين ID للزر إلغاء
        self.cancel_button.setObjectName("cancel_button")
        
    def handle_login(self):
        """معالجة تسجيل الدخول"""
        username = self.username_input.text().strip()
        password = self.password_input.text()
        
        # التحقق من الحقول
        if not username:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم المستخدم")
            self.username_input.setFocus()
            return
            
        if not password:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال كلمة المرور")
            self.password_input.setFocus()
            return
        
        # تعطيل الأزرار أثناء المعالجة
        self.login_button.setEnabled(False)
        self.login_button.setText("جاري التحقق...")
        
        try:
            # محاولة تسجيل الدخول
            success, message, user = self.auth_service.login(username, password)
            
            if success:
                QMessageBox.information(self, "نجح تسجيل الدخول", message)
                self.login_successful.emit(user)
                self.close()
            else:
                QMessageBox.warning(self, "فشل تسجيل الدخول", message)
                self.password_input.clear()
                self.password_input.setFocus()
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ غير متوقع: {str(e)}")
            
        finally:
            # إعادة تفعيل الأزرار
            self.login_button.setEnabled(True)
            self.login_button.setText("تسجيل الدخول")
    
    def center_on_screen(self):
        """توسيط النافذة على الشاشة"""
        screen = QApplication.desktop().screenGeometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)
    
    def showEvent(self, event):
        """عند إظهار النافذة"""
        super().showEvent(event)
        self.center_on_screen()
        
        # إنشاء المدير الافتراضي إذا لم يكن موجوداً
        self.auth_service.create_default_admin()
    
    def closeEvent(self, event):
        """عند إغلاق النافذة"""
        # إذا لم يتم تسجيل الدخول، إغلاق التطبيق
        if not self.auth_service.is_authenticated():
            QApplication.quit()
        event.accept()
