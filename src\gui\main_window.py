"""
النافذة الرئيسية للتطبيق
Main Application Window
"""
import sys
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                            QTabWidget, QLabel, QPushButton, QMenuBar, QMenu,
                            QAction, QStatusBar, QMessageBox, QFrame, QGridLayout)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QIcon
from datetime import datetime
from ..services.auth_service import AuthService
from ..services.family_service import FamilyService
from .family_management import FamilyManagementWidget
from .reports import ReportsWidget

class MainWindow(QMainWindow):
    """النافذة الرئيسية"""
    
    def __init__(self, user):
        super().__init__()
        self.current_user = user
        self.auth_service = AuthService()
        self.auth_service.current_user = user
        self.family_service = FamilyService()
        
        self.setup_ui()
        self.setup_menu()
        self.setup_status_bar()
        self.load_dashboard_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle(f"نظام إدارة المخيم - مرحباً {self.current_user.full_name}")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)
        
        # الويدجت الرئيسي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # شريط المعلومات العلوي
        self.create_info_bar(main_layout)
        
        # التبويبات الرئيسية
        self.tab_widget = QTabWidget()
        self.tab_widget.setFont(QFont("Arial", 10))
        
        # تبويب لوحة التحكم
        self.dashboard_widget = self.create_dashboard()
        self.tab_widget.addTab(self.dashboard_widget, "لوحة التحكم")
        
        # تبويب إدارة الأسر
        if self.auth_service.has_permission("read"):
            self.family_management = FamilyManagementWidget(self.auth_service)
            self.tab_widget.addTab(self.family_management, "إدارة الأسر")
        
        # تبويب التقارير
        if self.auth_service.has_permission("reports"):
            self.reports_widget = ReportsWidget(self.auth_service)
            self.tab_widget.addTab(self.reports_widget, "التقارير والإحصائيات")
        
        # تبويب توزيع المساعدات
        if self.auth_service.has_permission("distribute_aid") or self.auth_service.has_permission("read"):
            aid_widget = QWidget()
            aid_layout = QVBoxLayout(aid_widget)
            aid_layout.addWidget(QLabel("وحدة توزيع المساعدات - قيد التطوير"))
            self.tab_widget.addTab(aid_widget, "توزيع المساعدات")
        
        main_layout.addWidget(self.tab_widget)
        
        # تطبيق الأنماط
        self.setup_styles()
        
    def create_info_bar(self, parent_layout):
        """إنشاء شريط المعلومات العلوي"""
        info_frame = QFrame()
        info_frame.setFrameStyle(QFrame.Box)
        info_frame.setMaximumHeight(80)
        
        info_layout = QHBoxLayout(info_frame)
        
        # معلومات المستخدم
        user_info = QLabel(f"المستخدم: {self.current_user.full_name} | الدور: {self.current_user.get_role_display()}")
        user_info.setFont(QFont("Arial", 10, QFont.Bold))
        info_layout.addWidget(user_info)
        
        info_layout.addStretch()
        
        # التاريخ والوقت
        self.datetime_label = QLabel()
        self.datetime_label.setFont(QFont("Arial", 10))
        self.update_datetime()
        info_layout.addWidget(self.datetime_label)
        
        # تحديث الوقت كل ثانية
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_datetime)
        self.timer.start(1000)
        
        parent_layout.addWidget(info_frame)
        
    def create_dashboard(self):
        """إنشاء لوحة التحكم"""
        dashboard = QWidget()
        layout = QVBoxLayout(dashboard)
        
        # عنوان لوحة التحكم
        title = QLabel("لوحة التحكم الرئيسية")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # إطار الإحصائيات
        stats_frame = QFrame()
        stats_frame.setFrameStyle(QFrame.Box)
        stats_layout = QGridLayout(stats_frame)
        
        # بطاقات الإحصائيات
        self.stats_cards = {}
        
        # إجمالي الأسر
        self.stats_cards['families'] = self.create_stat_card("إجمالي الأسر", "0", "#3498db")
        stats_layout.addWidget(self.stats_cards['families'], 0, 0)
        
        # إجمالي الأفراد
        self.stats_cards['individuals'] = self.create_stat_card("إجمالي الأفراد", "0", "#2ecc71")
        stats_layout.addWidget(self.stats_cards['individuals'], 0, 1)
        
        # الأطفال
        self.stats_cards['children'] = self.create_stat_card("الأطفال", "0", "#f39c12")
        stats_layout.addWidget(self.stats_cards['children'], 0, 2)
        
        # البالغون
        self.stats_cards['adults'] = self.create_stat_card("البالغون", "0", "#9b59b6")
        stats_layout.addWidget(self.stats_cards['adults'], 0, 3)
        
        layout.addWidget(stats_frame)
        
        # أزرار سريعة
        quick_actions_frame = QFrame()
        quick_actions_frame.setFrameStyle(QFrame.Box)
        quick_actions_layout = QHBoxLayout(quick_actions_frame)
        
        if self.auth_service.has_permission("create"):
            add_family_btn = QPushButton("إضافة أسرة جديدة")
            add_family_btn.setMinimumHeight(50)
            add_family_btn.clicked.connect(self.add_new_family)
            quick_actions_layout.addWidget(add_family_btn)
        
        if self.auth_service.has_permission("reports"):
            export_btn = QPushButton("تصدير التقارير")
            export_btn.setMinimumHeight(50)
            export_btn.clicked.connect(self.export_reports)
            quick_actions_layout.addWidget(export_btn)
        
        refresh_btn = QPushButton("تحديث البيانات")
        refresh_btn.setMinimumHeight(50)
        refresh_btn.clicked.connect(self.load_dashboard_data)
        quick_actions_layout.addWidget(refresh_btn)
        
        layout.addWidget(quick_actions_frame)
        
        layout.addStretch()
        
        return dashboard
        
    def create_stat_card(self, title, value, color):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setFrameStyle(QFrame.Box)
        card.setMinimumHeight(120)
        
        layout = QVBoxLayout(card)
        
        # العنوان
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 12, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # القيمة
        value_label = QLabel(value)
        value_label.setFont(QFont("Arial", 24, QFont.Bold))
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setStyleSheet(f"color: {color};")
        layout.addWidget(value_label)
        
        # حفظ مرجع للقيمة للتحديث لاحقاً
        card.value_label = value_label
        
        return card
        
    def setup_menu(self):
        """إعداد شريط القوائم"""
        menubar = self.menuBar()
        
        # قائمة الملف
        file_menu = menubar.addMenu("ملف")
        
        if self.auth_service.has_permission("create"):
            new_family_action = QAction("أسرة جديدة", self)
            new_family_action.triggered.connect(self.add_new_family)
            file_menu.addAction(new_family_action)
            
            file_menu.addSeparator()
        
        if self.auth_service.has_permission("reports"):
            export_action = QAction("تصدير البيانات", self)
            export_action.triggered.connect(self.export_reports)
            file_menu.addAction(export_action)
            
            file_menu.addSeparator()
        
        logout_action = QAction("تسجيل الخروج", self)
        logout_action.triggered.connect(self.logout)
        file_menu.addAction(logout_action)
        
        exit_action = QAction("خروج", self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # قائمة المساعدة
        help_menu = menubar.addMenu("مساعدة")
        
        about_action = QAction("حول البرنامج", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
        
    def setup_status_bar(self):
        """إعداد شريط الحالة"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("جاهز")
        
    def setup_styles(self):
        """إعداد الأنماط"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QTabWidget::pane {
                border: 1px solid #c0c0c0;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #e0e0e0;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 2px solid #366092;
            }
            QFrame {
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 5px;
                margin: 5px;
                padding: 10px;
            }
            QPushButton {
                background-color: #366092;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px;
                font-weight: bold;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #2d4f7a;
            }
            QPushButton:pressed {
                background-color: #1e3a5f;
            }
        """)
        
    def load_dashboard_data(self):
        """تحميل بيانات لوحة التحكم"""
        try:
            stats = self.family_service.get_statistics()
            
            self.stats_cards['families'].value_label.setText(str(stats.get('total_families', 0)))
            self.stats_cards['individuals'].value_label.setText(str(stats.get('total_individuals', 0)))
            self.stats_cards['children'].value_label.setText(str(stats.get('children_count', 0)))
            self.stats_cards['adults'].value_label.setText(str(stats.get('adults_count', 0)))
            
            self.status_bar.showMessage("تم تحديث البيانات بنجاح")
            
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل البيانات: {str(e)}")
            
    def update_datetime(self):
        """تحديث التاريخ والوقت"""
        now = datetime.now()
        self.datetime_label.setText(now.strftime("%Y-%m-%d %H:%M:%S"))
        
    def add_new_family(self):
        """إضافة أسرة جديدة"""
        if hasattr(self, 'family_management'):
            self.tab_widget.setCurrentWidget(self.family_management)
            self.family_management.show_add_family_dialog()
        
    def export_reports(self):
        """تصدير التقارير"""
        if hasattr(self, 'reports_widget'):
            self.tab_widget.setCurrentWidget(self.reports_widget)
            
    def logout(self):
        """تسجيل الخروج"""
        reply = QMessageBox.question(self, "تسجيل الخروج", 
                                   "هل أنت متأكد من تسجيل الخروج؟",
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            self.auth_service.logout()
            self.close()
            
            # إظهار نافذة تسجيل الدخول مرة أخرى
            from .login_window import LoginWindow
            self.login_window = LoginWindow()
            self.login_window.show()
            
    def show_about(self):
        """إظهار معلومات حول البرنامج"""
        QMessageBox.about(self, "حول البرنامج", 
                         "نظام إدارة المخيم\n"
                         "الإصدار 1.0.0\n\n"
                         "نظام شامل لإدارة مخيمات اللاجئين\n"
                         "يوفر أدوات قوية لإدارة البيانات والتقارير")
        
    def closeEvent(self, event):
        """عند إغلاق النافذة"""
        reply = QMessageBox.question(self, "إغلاق البرنامج",
                                   "هل أنت متأكد من إغلاق البرنامج؟",
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            event.accept()
        else:
            event.ignore()
