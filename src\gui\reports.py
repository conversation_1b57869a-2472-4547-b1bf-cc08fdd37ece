"""
وحدة التقارير والإحصائيات
Reports and Statistics Widget
"""
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QFrame, QGridLayout, QGroupBox,
                            QTableWidget, QTableWidgetItem, QHeaderView,
                            QFileDialog, QMessageBox, QComboBox, QDateEdit,
                            QFormLayout, QTextEdit, QCheckBox, QSpinBox)
from PyQt5.QtCore import Qt, QDate, QThread, pyqtSignal
from PyQt5.QtGui import QFont, QPalette, QColor
from datetime import datetime, date, timedelta
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import matplotlib
matplotlib.use('Qt5Agg')

from ..services.family_service import FamilyService
from ..services.excel_service import ExcelService

class ReportsWidget(QWidget):
    """وحدة التقارير والإحصائيات"""
    
    def __init__(self, auth_service):
        super().__init__()
        self.auth_service = auth_service
        self.family_service = FamilyService()
        self.excel_service = ExcelService()
        
        self.setup_ui()
        self.load_statistics()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        
        # عنوان الصفحة
        title = QLabel("التقارير والإحصائيات")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # إطار الإحصائيات السريعة
        stats_frame = self.create_quick_stats_frame()
        layout.addWidget(stats_frame)
        
        # إطار الرسوم البيانية
        charts_frame = self.create_charts_frame()
        layout.addWidget(charts_frame)
        
        # إطار التقارير المخصصة
        custom_reports_frame = self.create_custom_reports_frame()
        layout.addWidget(custom_reports_frame)
        
        # تطبيق الأنماط
        self.setup_styles()
        
    def create_quick_stats_frame(self):
        """إنشاء إطار الإحصائيات السريعة"""
        frame = QFrame()
        frame.setFrameStyle(QFrame.Box)
        layout = QVBoxLayout(frame)
        
        # عنوان
        title = QLabel("الإحصائيات السريعة")
        title.setFont(QFont("Arial", 12, QFont.Bold))
        layout.addWidget(title)
        
        # شبكة الإحصائيات
        stats_grid = QGridLayout()
        
        # بطاقات الإحصائيات
        self.stats_cards = {}
        
        # إجمالي الأسر
        self.stats_cards['families'] = self.create_stat_card("إجمالي الأسر", "0", "#3498db")
        stats_grid.addWidget(self.stats_cards['families'], 0, 0)
        
        # إجمالي الأفراد
        self.stats_cards['individuals'] = self.create_stat_card("إجمالي الأفراد", "0", "#2ecc71")
        stats_grid.addWidget(self.stats_cards['individuals'], 0, 1)
        
        # الأطفال
        self.stats_cards['children'] = self.create_stat_card("الأطفال (أقل من 18)", "0", "#f39c12")
        stats_grid.addWidget(self.stats_cards['children'], 0, 2)
        
        # البالغون
        self.stats_cards['adults'] = self.create_stat_card("البالغون", "0", "#9b59b6")
        stats_grid.addWidget(self.stats_cards['adults'], 0, 3)
        
        # متوسط حجم الأسرة
        self.stats_cards['avg_family_size'] = self.create_stat_card("متوسط حجم الأسرة", "0", "#e74c3c")
        stats_grid.addWidget(self.stats_cards['avg_family_size'], 1, 0)
        
        # الوافدون الجدد (آخر 7 أيام)
        self.stats_cards['new_arrivals'] = self.create_stat_card("وافدون جدد (7 أيام)", "0", "#1abc9c")
        stats_grid.addWidget(self.stats_cards['new_arrivals'], 1, 1)
        
        # الذكور
        self.stats_cards['males'] = self.create_stat_card("الذكور", "0", "#34495e")
        stats_grid.addWidget(self.stats_cards['males'], 1, 2)
        
        # الإناث
        self.stats_cards['females'] = self.create_stat_card("الإناث", "0", "#e67e22")
        stats_grid.addWidget(self.stats_cards['females'], 1, 3)
        
        layout.addLayout(stats_grid)
        
        # زر التحديث
        refresh_button = QPushButton("تحديث الإحصائيات")
        refresh_button.clicked.connect(self.load_statistics)
        layout.addWidget(refresh_button)
        
        return frame
        
    def create_stat_card(self, title, value, color):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setFrameStyle(QFrame.Box)
        card.setMinimumHeight(100)
        card.setMaximumHeight(120)
        
        layout = QVBoxLayout(card)
        
        # العنوان
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 10, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setWordWrap(True)
        layout.addWidget(title_label)
        
        # القيمة
        value_label = QLabel(value)
        value_label.setFont(QFont("Arial", 20, QFont.Bold))
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setStyleSheet(f"color: {color};")
        layout.addWidget(value_label)
        
        # حفظ مرجع للقيمة للتحديث لاحقاً
        card.value_label = value_label
        
        return card
        
    def create_charts_frame(self):
        """إنشاء إطار الرسوم البيانية"""
        frame = QFrame()
        frame.setFrameStyle(QFrame.Box)
        layout = QVBoxLayout(frame)
        
        # عنوان
        title = QLabel("الرسوم البيانية")
        title.setFont(QFont("Arial", 12, QFont.Bold))
        layout.addWidget(title)
        
        # منطقة الرسوم البيانية
        charts_layout = QHBoxLayout()
        
        # رسم بياني للفئات العمرية
        self.age_chart = self.create_age_distribution_chart()
        charts_layout.addWidget(self.age_chart)
        
        # رسم بياني للجنس
        self.gender_chart = self.create_gender_distribution_chart()
        charts_layout.addWidget(self.gender_chart)
        
        layout.addLayout(charts_layout)
        
        return frame
        
    def create_age_distribution_chart(self):
        """إنشاء رسم بياني للتوزيع العمري"""
        figure = Figure(figsize=(6, 4))
        canvas = FigureCanvas(figure)
        
        # حفظ مرجع للرسم البياني
        self.age_figure = figure
        
        return canvas
        
    def create_gender_distribution_chart(self):
        """إنشاء رسم بياني لتوزيع الجنس"""
        figure = Figure(figsize=(6, 4))
        canvas = FigureCanvas(figure)
        
        # حفظ مرجع للرسم البياني
        self.gender_figure = figure
        
        return canvas
        
    def create_custom_reports_frame(self):
        """إنشاء إطار التقارير المخصصة"""
        frame = QFrame()
        frame.setFrameStyle(QFrame.Box)
        layout = QVBoxLayout(frame)
        
        # عنوان
        title = QLabel("التقارير المخصصة")
        title.setFont(QFont("Arial", 12, QFont.Bold))
        layout.addWidget(title)
        
        # أزرار التقارير
        reports_layout = QGridLayout()
        
        # تقرير الأسر الجديدة
        new_families_btn = QPushButton("تقرير الأسر الجديدة")
        new_families_btn.clicked.connect(self.generate_new_families_report)
        reports_layout.addWidget(new_families_btn, 0, 0)
        
        # تقرير التوزيع الجغرافي
        geographic_btn = QPushButton("التوزيع الجغرافي")
        geographic_btn.clicked.connect(self.generate_geographic_report)
        reports_layout.addWidget(geographic_btn, 0, 1)
        
        # تقرير الاحتياجات الخاصة
        special_needs_btn = QPushButton("تقرير الاحتياجات الخاصة")
        special_needs_btn.clicked.connect(self.generate_special_needs_report)
        reports_layout.addWidget(special_needs_btn, 0, 2)
        
        # تقرير الحالات الطبية
        medical_btn = QPushButton("تقرير الحالات الطبية")
        medical_btn.clicked.connect(self.generate_medical_report)
        reports_layout.addWidget(medical_btn, 1, 0)
        
        # تقرير الفئات العمرية
        age_groups_btn = QPushButton("تقرير الفئات العمرية")
        age_groups_btn.clicked.connect(self.generate_age_groups_report)
        reports_layout.addWidget(age_groups_btn, 1, 1)
        
        # تقرير شامل
        comprehensive_btn = QPushButton("تقرير شامل")
        comprehensive_btn.clicked.connect(self.generate_comprehensive_report)
        reports_layout.addWidget(comprehensive_btn, 1, 2)
        
        layout.addLayout(reports_layout)
        
        return frame
        
    def setup_styles(self):
        """إعداد الأنماط"""
        self.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 5px;
                padding: 10px;
                margin: 5px;
            }
            QPushButton {
                background-color: #366092;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 10px;
                font-weight: bold;
                min-height: 30px;
            }
            QPushButton:hover {
                background-color: #2d4f7a;
            }
            QPushButton:pressed {
                background-color: #1e3a5f;
            }
        """)
        
    def load_statistics(self):
        """تحميل الإحصائيات"""
        try:
            # الحصول على الإحصائيات الأساسية
            stats = self.family_service.get_statistics()
            
            # تحديث البطاقات
            self.stats_cards['families'].value_label.setText(str(stats.get('total_families', 0)))
            self.stats_cards['individuals'].value_label.setText(str(stats.get('total_individuals', 0)))
            self.stats_cards['children'].value_label.setText(str(stats.get('children_count', 0)))
            self.stats_cards['adults'].value_label.setText(str(stats.get('adults_count', 0)))
            self.stats_cards['avg_family_size'].value_label.setText(str(stats.get('average_family_size', 0)))
            
            # حساب الوافدين الجدد
            new_arrivals = self.calculate_new_arrivals()
            self.stats_cards['new_arrivals'].value_label.setText(str(new_arrivals))
            
            # حساب توزيع الجنس
            gender_stats = self.calculate_gender_distribution()
            self.stats_cards['males'].value_label.setText(str(gender_stats.get('males', 0)))
            self.stats_cards['females'].value_label.setText(str(gender_stats.get('females', 0)))
            
            # تحديث الرسوم البيانية
            self.update_charts()
            
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل الإحصائيات: {str(e)}")
            
    def calculate_new_arrivals(self):
        """حساب عدد الوافدين الجدد في آخر 7 أيام"""
        try:
            families = self.family_service.get_all_families()
            seven_days_ago = date.today() - timedelta(days=7)
            
            new_arrivals = 0
            for family in families:
                if family.arrival_date and family.arrival_date >= seven_days_ago:
                    new_arrivals += family.get_total_members()
            
            return new_arrivals
        except:
            return 0
            
    def calculate_gender_distribution(self):
        """حساب توزيع الجنس"""
        try:
            families = self.family_service.get_all_families()
            males = 0
            females = 0
            
            for family in families:
                # رب الأسرة
                if family.head_gender == "ذكر":
                    males += 1
                elif family.head_gender == "أنثى":
                    females += 1
                
                # أفراد الأسرة
                for individual in family.individuals:
                    if individual.gender == "ذكر":
                        males += 1
                    elif individual.gender == "أنثى":
                        females += 1
            
            return {'males': males, 'females': females}
        except:
            return {'males': 0, 'females': 0}
            
    def update_charts(self):
        """تحديث الرسوم البيانية"""
        try:
            # تحديث رسم التوزيع العمري
            self.update_age_distribution_chart()
            
            # تحديث رسم توزيع الجنس
            self.update_gender_distribution_chart()
            
        except Exception as e:
            print(f"خطأ في تحديث الرسوم البيانية: {str(e)}")
            
    def update_age_distribution_chart(self):
        """تحديث رسم التوزيع العمري"""
        try:
            families = self.family_service.get_all_families()
            age_groups = {
                'أطفال (0-12)': 0,
                'مراهقون (13-17)': 0,
                'شباب (18-30)': 0,
                'بالغون (31-50)': 0,
                'كبار السن (51+)': 0
            }
            
            for family in families:
                # رب الأسرة (تقدير العمر من تاريخ الميلاد)
                if family.head_birth_date:
                    age = (date.today() - family.head_birth_date).days // 365
                    if age <= 12:
                        age_groups['أطفال (0-12)'] += 1
                    elif age <= 17:
                        age_groups['مراهقون (13-17)'] += 1
                    elif age <= 30:
                        age_groups['شباب (18-30)'] += 1
                    elif age <= 50:
                        age_groups['بالغون (31-50)'] += 1
                    else:
                        age_groups['كبار السن (51+)'] += 1
                
                # أفراد الأسرة
                for individual in family.individuals:
                    age = individual.get_age()
                    if age <= 12:
                        age_groups['أطفال (0-12)'] += 1
                    elif age <= 17:
                        age_groups['مراهقون (13-17)'] += 1
                    elif age <= 30:
                        age_groups['شباب (18-30)'] += 1
                    elif age <= 50:
                        age_groups['بالغون (31-50)'] += 1
                    else:
                        age_groups['كبار السن (51+)'] += 1
            
            # رسم البيانات
            self.age_figure.clear()
            ax = self.age_figure.add_subplot(111)
            
            labels = list(age_groups.keys())
            values = list(age_groups.values())
            
            ax.pie(values, labels=labels, autopct='%1.1f%%', startangle=90)
            ax.set_title('التوزيع العمري')
            
            self.age_figure.tight_layout()
            self.age_chart.draw()
            
        except Exception as e:
            print(f"خطأ في رسم التوزيع العمري: {str(e)}")
            
    def update_gender_distribution_chart(self):
        """تحديث رسم توزيع الجنس"""
        try:
            gender_stats = self.calculate_gender_distribution()
            
            # رسم البيانات
            self.gender_figure.clear()
            ax = self.gender_figure.add_subplot(111)
            
            labels = ['ذكور', 'إناث']
            values = [gender_stats['males'], gender_stats['females']]
            colors = ['#3498db', '#e74c3c']
            
            ax.pie(values, labels=labels, autopct='%1.1f%%', colors=colors, startangle=90)
            ax.set_title('توزيع الجنس')
            
            self.gender_figure.tight_layout()
            self.gender_chart.draw()
            
        except Exception as e:
            print(f"خطأ في رسم توزيع الجنس: {str(e)}")
            
    def generate_new_families_report(self):
        """إنشاء تقرير الأسر الجديدة"""
        # سيتم تنفيذ هذا لاحقاً
        QMessageBox.information(self, "قيد التطوير", "هذه الميزة قيد التطوير")
        
    def generate_geographic_report(self):
        """إنشاء تقرير التوزيع الجغرافي"""
        QMessageBox.information(self, "قيد التطوير", "هذه الميزة قيد التطوير")
        
    def generate_special_needs_report(self):
        """إنشاء تقرير الاحتياجات الخاصة"""
        QMessageBox.information(self, "قيد التطوير", "هذه الميزة قيد التطوير")
        
    def generate_medical_report(self):
        """إنشاء تقرير الحالات الطبية"""
        QMessageBox.information(self, "قيد التطوير", "هذه الميزة قيد التطوير")
        
    def generate_age_groups_report(self):
        """إنشاء تقرير الفئات العمرية"""
        QMessageBox.information(self, "قيد التطوير", "هذه الميزة قيد التطوير")
        
    def generate_comprehensive_report(self):
        """إنشاء تقرير شامل"""
        QMessageBox.information(self, "قيد التطوير", "هذه الميزة قيد التطوير")
