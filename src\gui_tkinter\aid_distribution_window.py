"""
نافذة إدارة توزيع المساعدات
Aid Distribution Management Window
"""
import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, date
import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.services.family_service import FamilyService
from src.models.aid_distribution import AidDistribution
from src.config.database import get_db_session

class AidDistributionWindow:
    """نافذة إدارة توزيع المساعدات"""
    
    def __init__(self, parent, current_user):
        self.parent = parent
        self.current_user = current_user
        self.family_service = FamilyService()
        self.db = get_db_session()
        
        # أنواع المساعدات
        self.aid_types = [
            "سلة غذائية",
            "بطانيات",
            "مواد طبية",
            "ملابس",
            "مواد تنظيف",
            "مياه شرب",
            "مساعدات نقدية",
            "أخرى"
        ]
        
        self.window = tk.Toplevel(parent)
        self.setup_window()
        self.create_widgets()
        self.load_data()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.window.title("إدارة توزيع المساعدات")
        self.window.geometry("1000x700")
        self.window.resizable(True, True)
        
        # توسيط النافذة
        self.center_window()
        
        # جعل النافذة modal
        self.window.transient(self.parent)
        self.window.grab_set()
        
    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = 1000
        height = 700
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
        
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان
        title_label = ttk.Label(self.window, text="إدارة توزيع المساعدات", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # إنشاء التبويبات
        self.notebook = ttk.Notebook(self.window)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # تبويب توزيع جديد
        self.create_new_distribution_tab()
        
        # تبويب سجل التوزيع
        self.create_distribution_history_tab()
        
        # تبويب التقارير
        self.create_reports_tab()
        
        # أزرار التحكم
        self.create_control_buttons()
        
    def create_new_distribution_tab(self):
        """إنشاء تبويب التوزيع الجديد"""
        new_dist_frame = ttk.Frame(self.notebook)
        self.notebook.add(new_dist_frame, text="توزيع جديد")
        
        main_frame = ttk.Frame(new_dist_frame, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # معلومات التوزيع
        info_frame = ttk.LabelFrame(main_frame, text="معلومات التوزيع", padding="10")
        info_frame.pack(fill=tk.X, pady=(0, 20))
        
        # التاريخ
        ttk.Label(info_frame, text="تاريخ التوزيع:", font=("Arial", 10, "bold")).grid(row=0, column=0, sticky=tk.W, pady=5)
        
        date_frame = ttk.Frame(info_frame)
        date_frame.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        self.dist_day_var = tk.StringVar()
        self.dist_month_var = tk.StringVar()
        self.dist_year_var = tk.StringVar()
        
        # تعيين التاريخ الحالي
        today = date.today()
        self.dist_day_var.set(str(today.day))
        self.dist_month_var.set(str(today.month))
        self.dist_year_var.set(str(today.year))
        
        ttk.Label(date_frame, text="اليوم:").pack(side=tk.LEFT)
        day_combo = ttk.Combobox(date_frame, textvariable=self.dist_day_var, width=5, state="readonly")
        day_combo['values'] = [str(i) for i in range(1, 32)]
        day_combo.pack(side=tk.LEFT, padx=(5, 10))
        
        ttk.Label(date_frame, text="الشهر:").pack(side=tk.LEFT)
        month_combo = ttk.Combobox(date_frame, textvariable=self.dist_month_var, width=5, state="readonly")
        month_combo['values'] = [str(i) for i in range(1, 13)]
        month_combo.pack(side=tk.LEFT, padx=(5, 10))
        
        ttk.Label(date_frame, text="السنة:").pack(side=tk.LEFT)
        year_combo = ttk.Combobox(date_frame, textvariable=self.dist_year_var, width=8, state="readonly")
        current_year = datetime.now().year
        year_combo['values'] = [str(i) for i in range(2020, current_year + 1)]
        year_combo.pack(side=tk.LEFT, padx=(5, 0))
        
        # نوع المساعدة
        ttk.Label(info_frame, text="نوع المساعدة:", font=("Arial", 10, "bold")).grid(row=1, column=0, sticky=tk.W, pady=5)
        self.aid_type_var = tk.StringVar()
        aid_type_combo = ttk.Combobox(info_frame, textvariable=self.aid_type_var, width=30)
        aid_type_combo['values'] = self.aid_types
        aid_type_combo.grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # الكمية
        ttk.Label(info_frame, text="الكمية:", font=("Arial", 10, "bold")).grid(row=2, column=0, sticky=tk.W, pady=5)
        self.quantity_var = tk.StringVar()
        ttk.Entry(info_frame, textvariable=self.quantity_var, width=30).grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # الوحدة
        ttk.Label(info_frame, text="الوحدة:", font=("Arial", 10, "bold")).grid(row=3, column=0, sticky=tk.W, pady=5)
        self.unit_var = tk.StringVar()
        unit_combo = ttk.Combobox(info_frame, textvariable=self.unit_var, width=30)
        unit_combo['values'] = ["قطعة", "كيلو", "لتر", "علبة", "كيس", "صندوق", "أخرى"]
        unit_combo.grid(row=3, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # ملاحظات
        ttk.Label(info_frame, text="ملاحظات:", font=("Arial", 10, "bold")).grid(row=4, column=0, sticky=tk.NW, pady=5)
        self.notes_text = tk.Text(info_frame, height=3, width=30)
        self.notes_text.grid(row=4, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # اختيار الأسر
        families_frame = ttk.LabelFrame(main_frame, text="اختيار الأسر المستفيدة", padding="10")
        families_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
        
        # شريط البحث
        search_frame = ttk.Frame(families_frame)
        search_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(search_frame, text="البحث:").pack(side=tk.LEFT)
        self.family_search_var = tk.StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=self.family_search_var, width=30)
        search_entry.pack(side=tk.LEFT, padx=(5, 10))
        search_entry.bind('<KeyRelease>', self.search_families)
        
        ttk.Button(search_frame, text="تحديد الكل", 
                  command=self.select_all_families).pack(side=tk.LEFT, padx=5)
        ttk.Button(search_frame, text="إلغاء التحديد", 
                  command=self.deselect_all_families).pack(side=tk.LEFT, padx=5)
        
        # جدول الأسر
        self.families_tree = ttk.Treeview(families_frame, 
                                         columns=("family_id", "head_name", "phone", "tent_number", "members"), 
                                         show="tree headings", height=10)
        
        self.families_tree.heading("#0", text="✓")
        self.families_tree.heading("family_id", text="رقم الأسرة")
        self.families_tree.heading("head_name", text="اسم رب الأسرة")
        self.families_tree.heading("phone", text="رقم الهاتف")
        self.families_tree.heading("tent_number", text="رقم الخيمة")
        self.families_tree.heading("members", text="عدد الأفراد")
        
        self.families_tree.column("#0", width=50)
        self.families_tree.column("family_id", width=100)
        self.families_tree.column("head_name", width=200)
        self.families_tree.column("phone", width=120)
        self.families_tree.column("tent_number", width=100)
        self.families_tree.column("members", width=100)
        
        # شريط التمرير
        families_scrollbar = ttk.Scrollbar(families_frame, orient=tk.VERTICAL, command=self.families_tree.yview)
        self.families_tree.configure(yscrollcommand=families_scrollbar.set)
        
        self.families_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        families_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط النقر لتحديد الأسر
        self.families_tree.bind("<Button-1>", self.toggle_family_selection)
        
        # أزرار التوزيع
        dist_buttons_frame = ttk.Frame(main_frame)
        dist_buttons_frame.pack(fill=tk.X)
        
        ttk.Button(dist_buttons_frame, text="تسجيل التوزيع", 
                  command=self.record_distribution).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(dist_buttons_frame, text="مسح النموذج", 
                  command=self.clear_form).pack(side=tk.LEFT)
        
    def create_distribution_history_tab(self):
        """إنشاء تبويب سجل التوزيع"""
        history_frame = ttk.Frame(self.notebook)
        self.notebook.add(history_frame, text="سجل التوزيع")
        
        # شريط الأدوات
        toolbar_frame = ttk.Frame(history_frame)
        toolbar_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(toolbar_frame, text="البحث:").pack(side=tk.LEFT)
        self.history_search_var = tk.StringVar()
        search_entry = ttk.Entry(toolbar_frame, textvariable=self.history_search_var, width=30)
        search_entry.pack(side=tk.LEFT, padx=(5, 10))
        search_entry.bind('<KeyRelease>', self.search_distribution_history)
        
        ttk.Button(toolbar_frame, text="تحديث", 
                  command=self.load_distribution_history).pack(side=tk.LEFT, padx=5)
        
        # جدول سجل التوزيع
        self.history_tree = ttk.Treeview(history_frame, 
                                        columns=("date", "family_id", "head_name", "aid_type", "quantity", "unit"), 
                                        show="headings", height=20)
        
        self.history_tree.heading("date", text="التاريخ")
        self.history_tree.heading("family_id", text="رقم الأسرة")
        self.history_tree.heading("head_name", text="اسم رب الأسرة")
        self.history_tree.heading("aid_type", text="نوع المساعدة")
        self.history_tree.heading("quantity", text="الكمية")
        self.history_tree.heading("unit", text="الوحدة")
        
        self.history_tree.column("date", width=100)
        self.history_tree.column("family_id", width=100)
        self.history_tree.column("head_name", width=200)
        self.history_tree.column("aid_type", width=150)
        self.history_tree.column("quantity", width=80)
        self.history_tree.column("unit", width=80)
        
        # شريط التمرير للسجل
        history_scrollbar = ttk.Scrollbar(history_frame, orient=tk.VERTICAL, command=self.history_tree.yview)
        self.history_tree.configure(yscrollcommand=history_scrollbar.set)
        
        self.history_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 0), pady=5)
        history_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, padx=(0, 10), pady=5)
        
    def create_reports_tab(self):
        """إنشاء تبويب التقارير"""
        reports_frame = ttk.Frame(self.notebook)
        self.notebook.add(reports_frame, text="التقارير")
        
        main_frame = ttk.Frame(reports_frame, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # عنوان
        title_label = ttk.Label(main_frame, text="تقارير توزيع المساعدات", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # إحصائيات سريعة
        stats_frame = ttk.LabelFrame(main_frame, text="إحصائيات سريعة", padding="10")
        stats_frame.pack(fill=tk.X, pady=(0, 20))
        
        self.stats_text = tk.Text(stats_frame, height=8, state="disabled")
        self.stats_text.pack(fill=tk.BOTH, expand=True)
        
        # أزرار التقارير
        reports_buttons_frame = ttk.Frame(main_frame)
        reports_buttons_frame.pack(fill=tk.X)
        
        ttk.Button(reports_buttons_frame, text="تقرير التوزيع الشهري", 
                  command=self.generate_monthly_report).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(reports_buttons_frame, text="الأسر التي لم تستلم مساعدات", 
                  command=self.generate_non_recipients_report).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(reports_buttons_frame, text="تصدير السجل إلى Excel", 
                  command=self.export_distribution_history).pack(side=tk.LEFT)
        
    def create_control_buttons(self):
        """إنشاء أزرار التحكم"""
        buttons_frame = ttk.Frame(self.window)
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(buttons_frame, text="إغلاق", 
                  command=self.window.destroy).pack(side=tk.RIGHT, padx=5)
        
        ttk.Button(buttons_frame, text="مساعدة", 
                  command=self.show_help).pack(side=tk.LEFT, padx=5)
    
    def load_data(self):
        """تحميل البيانات"""
        self.load_families()
        self.load_distribution_history()
        self.update_statistics()
    
    def load_families(self):
        """تحميل قائمة الأسر"""
        try:
            # مسح البيانات الحالية
            for item in self.families_tree.get_children():
                self.families_tree.delete(item)
            
            families = self.family_service.get_all_families()
            
            for family in families:
                self.families_tree.insert("", tk.END, text="☐", values=(
                    family.family_id,
                    family.head_of_family_name,
                    family.phone_number or "",
                    family.tent_number or "",
                    family.get_total_members()
                ), tags=("unselected",))
                
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل الأسر: {str(e)}")
    
    def search_families(self, event=None):
        """البحث في الأسر"""
        search_term = self.family_search_var.get().strip().lower()
        
        # مسح البيانات الحالية
        for item in self.families_tree.get_children():
            self.families_tree.delete(item)
        
        try:
            if search_term:
                families = self.family_service.search_families(search_term)
            else:
                families = self.family_service.get_all_families()
            
            for family in families:
                self.families_tree.insert("", tk.END, text="☐", values=(
                    family.family_id,
                    family.head_of_family_name,
                    family.phone_number or "",
                    family.tent_number or "",
                    family.get_total_members()
                ), tags=("unselected",))
                
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في البحث: {str(e)}")
    
    def toggle_family_selection(self, event):
        """تبديل تحديد الأسرة"""
        item = self.families_tree.selection()[0] if self.families_tree.selection() else None
        if not item:
            return
        
        current_tags = self.families_tree.item(item, "tags")
        if "selected" in current_tags:
            self.families_tree.item(item, text="☐", tags=("unselected",))
        else:
            self.families_tree.item(item, text="☑", tags=("selected",))
    
    def select_all_families(self):
        """تحديد جميع الأسر"""
        for item in self.families_tree.get_children():
            self.families_tree.item(item, text="☑", tags=("selected",))
    
    def deselect_all_families(self):
        """إلغاء تحديد جميع الأسر"""
        for item in self.families_tree.get_children():
            self.families_tree.item(item, text="☐", tags=("unselected",))
    
    def record_distribution(self):
        """تسجيل عملية التوزيع"""
        # التحقق من البيانات
        if not self.aid_type_var.get():
            messagebox.showerror("خطأ", "يرجى اختيار نوع المساعدة")
            return
        
        if not self.quantity_var.get():
            messagebox.showerror("خطأ", "يرجى إدخال الكمية")
            return
        
        # الحصول على الأسر المحددة
        selected_families = []
        for item in self.families_tree.get_children():
            if "selected" in self.families_tree.item(item, "tags"):
                values = self.families_tree.item(item, "values")
                selected_families.append(values[0])  # family_id
        
        if not selected_families:
            messagebox.showerror("خطأ", "يرجى اختيار أسرة واحدة على الأقل")
            return
        
        try:
            # تحضير تاريخ التوزيع
            distribution_date = date(
                int(self.dist_year_var.get()),
                int(self.dist_month_var.get()),
                int(self.dist_day_var.get())
            )
            
            # تسجيل التوزيع لكل أسرة محددة
            success_count = 0
            for family_id in selected_families:
                family = self.family_service.get_family_by_id(family_id)
                if family:
                    # إنشاء سجل توزيع جديد
                    distribution = AidDistribution(
                        family_id=family.id,
                        aid_type=self.aid_type_var.get(),
                        quantity=float(self.quantity_var.get()),
                        unit=self.unit_var.get(),
                        distribution_date=distribution_date,
                        distributed_by=self.current_user.full_name,
                        notes=self.notes_text.get(1.0, tk.END).strip() or None
                    )
                    
                    self.db.add(distribution)
                    success_count += 1
            
            self.db.commit()
            
            messagebox.showinfo("نجح", f"تم تسجيل التوزيع لـ {success_count} أسرة بنجاح")
            
            # تحديث البيانات
            self.load_distribution_history()
            self.update_statistics()
            self.clear_form()
            
        except Exception as e:
            self.db.rollback()
            messagebox.showerror("خطأ", f"فشل في تسجيل التوزيع: {str(e)}")
    
    def clear_form(self):
        """مسح النموذج"""
        self.aid_type_var.set("")
        self.quantity_var.set("")
        self.unit_var.set("")
        self.notes_text.delete(1.0, tk.END)
        self.deselect_all_families()
    
    def load_distribution_history(self):
        """تحميل سجل التوزيع"""
        try:
            # مسح البيانات الحالية
            for item in self.history_tree.get_children():
                self.history_tree.delete(item)
            
            # تحميل سجل التوزيع
            distributions = self.db.query(AidDistribution).order_by(AidDistribution.distribution_date.desc()).all()
            
            for dist in distributions:
                family = self.family_service.get_family_by_id(dist.family_id)
                family_id = family.family_id if family else "غير معروف"
                head_name = family.head_of_family_name if family else "غير معروف"
                
                self.history_tree.insert("", tk.END, values=(
                    dist.distribution_date.strftime('%Y-%m-%d'),
                    family_id,
                    head_name,
                    dist.aid_type,
                    dist.quantity,
                    dist.unit or ""
                ))
                
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل سجل التوزيع: {str(e)}")
    
    def search_distribution_history(self, event=None):
        """البحث في سجل التوزيع"""
        messagebox.showinfo("قيد التطوير", "ميزة البحث في السجل قيد التطوير")
    
    def update_statistics(self):
        """تحديث الإحصائيات"""
        try:
            total_distributions = self.db.query(AidDistribution).count()
            total_families_helped = self.db.query(AidDistribution.family_id).distinct().count()
            
            # إحصائيات حسب نوع المساعدة
            aid_type_stats = {}
            for aid_type in self.aid_types:
                count = self.db.query(AidDistribution).filter(AidDistribution.aid_type == aid_type).count()
                if count > 0:
                    aid_type_stats[aid_type] = count
            
            stats_text = f"""إحصائيات توزيع المساعدات:

إجمالي عمليات التوزيع: {total_distributions}
عدد الأسر المستفيدة: {total_families_helped}

التوزيع حسب نوع المساعدة:
"""
            
            for aid_type, count in aid_type_stats.items():
                stats_text += f"- {aid_type}: {count} عملية\n"
            
            self.stats_text.config(state="normal")
            self.stats_text.delete(1.0, tk.END)
            self.stats_text.insert(1.0, stats_text)
            self.stats_text.config(state="disabled")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحديث الإحصائيات: {str(e)}")
    
    def generate_monthly_report(self):
        """إنشاء تقرير شهري"""
        messagebox.showinfo("قيد التطوير", "تقرير التوزيع الشهري قيد التطوير")
    
    def generate_non_recipients_report(self):
        """تقرير الأسر التي لم تستلم مساعدات"""
        messagebox.showinfo("قيد التطوير", "تقرير الأسر غير المستفيدة قيد التطوير")
    
    def export_distribution_history(self):
        """تصدير سجل التوزيع إلى Excel"""
        messagebox.showinfo("قيد التطوير", "تصدير سجل التوزيع قيد التطوير")
    
    def show_help(self):
        """عرض المساعدة"""
        help_text = """مساعدة إدارة توزيع المساعدات

1. توزيع جديد:
   - اختر تاريخ التوزيع
   - حدد نوع المساعدة والكمية
   - اختر الأسر المستفيدة
   - سجل التوزيع

2. سجل التوزيع:
   - عرض جميع عمليات التوزيع السابقة
   - البحث والتصفية
   - تصدير البيانات

3. التقارير:
   - إحصائيات سريعة
   - تقارير شهرية
   - الأسر غير المستفيدة

4. نصائح:
   - يمكن تحديد عدة أسر في نفس الوقت
   - استخدم البحث للعثور على أسر محددة
   - راجع السجل للتأكد من عدم التكرار"""
        
        messagebox.showinfo("مساعدة", help_text)

if __name__ == "__main__":
    # للاختبار
    root = tk.Tk()
    root.withdraw()
    
    from src.services.auth_service import AuthService
    auth = AuthService()
    auth.create_default_admin()
    success, message, user = auth.login("admin", "admin123")
    
    if success:
        app = AidDistributionWindow(root, user)
        root.mainloop()
