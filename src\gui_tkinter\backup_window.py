"""
نافذة إدارة النسخ الاحتياطي والاستعادة
Backup and Restore Management Window
"""
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime
import sys
import os
import threading

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.services.backup_service import BackupService

class BackupWindow:
    """نافذة إدارة النسخ الاحتياطي والاستعادة"""

    def __init__(self, parent, current_user):
        self.parent = parent
        self.current_user = current_user
        self.backup_service = BackupService()

        self.window = tk.Toplevel(parent)
        self.setup_window()
        self.create_widgets()
        self.load_backups()

    def setup_window(self):
        """إعداد النافذة"""
        self.window.title("إدارة النسخ الاحتياطي والاستعادة")
        self.window.geometry("900x600")
        self.window.resizable(True, True)

        # توسيط النافذة
        self.center_window()

        # جعل النافذة modal
        self.window.transient(self.parent)
        self.window.grab_set()

    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = 900
        height = 600
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان
        title_label = ttk.Label(self.window, text="💾 إدارة النسخ الاحتياطي والاستعادة",
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=10)

        # إنشاء التبويبات
        self.notebook = ttk.Notebook(self.window)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # تبويب إنشاء نسخة احتياطية
        self.create_backup_tab()

        # تبويب إدارة النسخ الاحتياطية
        self.create_manage_tab()

        # تبويب الاستعادة
        self.create_restore_tab()

        # أزرار التحكم
        self.create_control_buttons()

    def create_backup_tab(self):
        """إنشاء تبويب إنشاء النسخة الاحتياطية"""
        backup_frame = ttk.Frame(self.notebook)
        self.notebook.add(backup_frame, text="إنشاء نسخة احتياطية")

        main_frame = ttk.Frame(backup_frame, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # معلومات النسخة الاحتياطية
        info_frame = ttk.LabelFrame(main_frame, text="معلومات النسخة الاحتياطية", padding="15")
        info_frame.pack(fill=tk.X, pady=(0, 20))

        # اسم النسخة الاحتياطية
        ttk.Label(info_frame, text="اسم النسخة الاحتياطية:", font=("Arial", 10, "bold")).grid(row=0, column=0, sticky=tk.W, pady=5)
        self.backup_name_var = tk.StringVar()
        default_name = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.backup_name_var.set(default_name)
        ttk.Entry(info_frame, textvariable=self.backup_name_var, width=40).grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        # خيارات النسخ الاحتياطي
        options_frame = ttk.LabelFrame(main_frame, text="خيارات النسخ الاحتياطي", padding="15")
        options_frame.pack(fill=tk.X, pady=(0, 20))

        self.include_files_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="تضمين الملفات الإضافية (قوالب Excel، ملفات التصدير)",
                       variable=self.include_files_var).pack(anchor=tk.W, pady=5)

        # معلومات سريعة
        quick_info_frame = ttk.LabelFrame(main_frame, text="معلومات سريعة", padding="15")
        quick_info_frame.pack(fill=tk.X, pady=(0, 20))

        info_text = """ما يتم نسخه احتياطياً:

✅ قاعدة البيانات الكاملة (جميع الأسر والأفراد والمستخدمين)
✅ ملفات التكوين والإعدادات
✅ قوالب Excel (اختياري)
✅ ملفات التصدير (اختياري)

💡 نصائح:
• يُنصح بإنشاء نسخة احتياطية يومياً
• احتفظ بعدة نسخ احتياطية في أماكن مختلفة
• تحقق من صحة النسخة الاحتياطية بعد إنشائها"""

        ttk.Label(quick_info_frame, text=info_text, font=("Arial", 10),
                 justify=tk.LEFT).pack(anchor=tk.W)

        # شريط التقدم
        progress_frame = ttk.Frame(main_frame)
        progress_frame.pack(fill=tk.X, pady=(0, 20))

        self.backup_progress_var = tk.DoubleVar()
        self.backup_progress_bar = ttk.Progressbar(progress_frame, variable=self.backup_progress_var,
                                                  maximum=100, mode='determinate')
        self.backup_progress_bar.pack(fill=tk.X, pady=(0, 5))

        self.backup_status_label = ttk.Label(progress_frame, text="جاهز لإنشاء نسخة احتياطية")
        self.backup_status_label.pack()

        # أزرار
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X)

        self.create_backup_btn = ttk.Button(buttons_frame, text="🔄 إنشاء نسخة احتياطية",
                                           command=self.create_backup)
        self.create_backup_btn.pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(buttons_frame, text="📁 فتح مجلد النسخ الاحتياطية",
                  command=self.open_backup_folder).pack(side=tk.LEFT)

    def create_manage_tab(self):
        """إنشاء تبويب إدارة النسخ الاحتياطية"""
        manage_frame = ttk.Frame(self.notebook)
        self.notebook.add(manage_frame, text="إدارة النسخ الاحتياطية")

        # شريط الأدوات
        toolbar_frame = ttk.Frame(manage_frame)
        toolbar_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(toolbar_frame, text="🔄 تحديث",
                  command=self.load_backups).pack(side=tk.LEFT, padx=5)
        ttk.Button(toolbar_frame, text="🗑️ حذف",
                  command=self.delete_backup).pack(side=tk.LEFT, padx=5)
        ttk.Button(toolbar_frame, text="✅ التحقق من الصحة",
                  command=self.verify_backup).pack(side=tk.LEFT, padx=5)
        ttk.Button(toolbar_frame, text="📊 تصدير تقرير",
                  command=self.export_backup_report).pack(side=tk.LEFT, padx=5)

        # جدول النسخ الاحتياطية
        self.backups_tree = ttk.Treeview(manage_frame,
                                        columns=("name", "size", "created", "description"),
                                        show="headings", height=15)

        self.backups_tree.heading("name", text="اسم النسخة")
        self.backups_tree.heading("size", text="الحجم (MB)")
        self.backups_tree.heading("created", text="تاريخ الإنشاء")
        self.backups_tree.heading("description", text="الوصف")

        self.backups_tree.column("name", width=200)
        self.backups_tree.column("size", width=100)
        self.backups_tree.column("created", width=150)
        self.backups_tree.column("description", width=300)

        # شريط التمرير
        backups_scrollbar = ttk.Scrollbar(manage_frame, orient=tk.VERTICAL,
                                         command=self.backups_tree.yview)
        self.backups_tree.configure(yscrollcommand=backups_scrollbar.set)

        self.backups_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 0), pady=5)
        backups_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, padx=(0, 10), pady=5)

        # ربط النقر المزدوج
        self.backups_tree.bind("<Double-1>", lambda e: self.show_backup_details())

    def create_restore_tab(self):
        """إنشاء تبويب الاستعادة"""
        restore_frame = ttk.Frame(self.notebook)
        self.notebook.add(restore_frame, text="استعادة النسخة الاحتياطية")

        main_frame = ttk.Frame(restore_frame, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # تحذير
        warning_frame = ttk.LabelFrame(main_frame, text="⚠️ تحذير مهم", padding="15")
        warning_frame.pack(fill=tk.X, pady=(0, 20))

        warning_text = """تحذير: عملية الاستعادة ستقوم بـ:

🔄 استبدال قاعدة البيانات الحالية بالكامل
🔄 استبدال ملفات التكوين
🔄 إنشاء نسخة احتياطية من الحالة الحالية قبل الاستعادة

⚠️ تأكد من أن النسخة الاحتياطية المختارة صحيحة وحديثة
⚠️ سيتم إعادة تشغيل التطبيق بعد الاستعادة"""

        warning_label = ttk.Label(warning_frame, text=warning_text, font=("Arial", 10),
                                 foreground="red", justify=tk.LEFT)
        warning_label.pack(anchor=tk.W)

        # اختيار ملف النسخة الاحتياطية
        file_frame = ttk.LabelFrame(main_frame, text="اختيار ملف النسخة الاحتياطية", padding="15")
        file_frame.pack(fill=tk.X, pady=(0, 20))

        self.restore_file_var = tk.StringVar()
        file_entry = ttk.Entry(file_frame, textvariable=self.restore_file_var, width=60, state="readonly")
        file_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))

        ttk.Button(file_frame, text="تصفح", command=self.browse_backup_file).pack(side=tk.RIGHT)

        # خيارات الاستعادة
        options_frame = ttk.LabelFrame(main_frame, text="خيارات الاستعادة", padding="15")
        options_frame.pack(fill=tk.X, pady=(0, 20))

        self.restore_database_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="استعادة قاعدة البيانات",
                       variable=self.restore_database_var).pack(anchor=tk.W, pady=2)

        self.restore_config_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="استعادة ملفات التكوين",
                       variable=self.restore_config_var).pack(anchor=tk.W, pady=2)

        # شريط التقدم
        restore_progress_frame = ttk.Frame(main_frame)
        restore_progress_frame.pack(fill=tk.X, pady=(0, 20))

        self.restore_progress_var = tk.DoubleVar()
        self.restore_progress_bar = ttk.Progressbar(restore_progress_frame, variable=self.restore_progress_var,
                                                   maximum=100, mode='indeterminate')
        self.restore_progress_bar.pack(fill=tk.X, pady=(0, 5))

        self.restore_status_label = ttk.Label(restore_progress_frame, text="اختر ملف النسخة الاحتياطية")
        self.restore_status_label.pack()

        # أزرار
        restore_buttons_frame = ttk.Frame(main_frame)
        restore_buttons_frame.pack(fill=tk.X)

        self.restore_btn = ttk.Button(restore_buttons_frame, text="🔄 بدء الاستعادة",
                                     command=self.restore_backup, state="disabled")
        self.restore_btn.pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(restore_buttons_frame, text="✅ التحقق من الملف",
                  command=self.verify_selected_backup).pack(side=tk.LEFT)

    def create_control_buttons(self):
        """إنشاء أزرار التحكم"""
        buttons_frame = ttk.Frame(self.window)
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Button(buttons_frame, text="إغلاق",
                  command=self.window.destroy).pack(side=tk.RIGHT, padx=5)

        ttk.Button(buttons_frame, text="مساعدة",
                  command=self.show_help).pack(side=tk.LEFT, padx=5)

    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        def backup_thread():
            try:
                self.backup_progress_var.set(0)
                self.backup_status_label.config(text="جاري إنشاء النسخة الاحتياطية...")
                self.create_backup_btn.config(state="disabled")
                self.window.update()

                # محاكاة التقدم
                for i in range(0, 101, 10):
                    self.backup_progress_var.set(i)
                    self.window.update()

                backup_name = self.backup_name_var.get().strip()
                include_files = self.include_files_var.get()

                success, message, backup_path = self.backup_service.create_backup(backup_name, include_files)

                self.backup_progress_var.set(100)

                if success:
                    self.backup_status_label.config(text="تم إنشاء النسخة الاحتياطية بنجاح ✅")
                    messagebox.showinfo("نجح", message)
                    self.load_backups()  # تحديث قائمة النسخ الاحتياطية

                    # إعادة تعيين اسم جديد
                    new_name = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    self.backup_name_var.set(new_name)
                else:
                    self.backup_status_label.config(text="فشل في إنشاء النسخة الاحتياطية ❌")
                    messagebox.showerror("خطأ", message)

            except Exception as e:
                self.backup_status_label.config(text="خطأ في إنشاء النسخة الاحتياطية ❌")
                messagebox.showerror("خطأ", f"خطأ غير متوقع: {str(e)}")
            finally:
                self.create_backup_btn.config(state="normal")

        # تشغيل في thread منفصل لتجنب تجميد الواجهة
        threading.Thread(target=backup_thread, daemon=True).start()

    def load_backups(self):
        """تحميل قائمة النسخ الاحتياطية"""
        try:
            # مسح البيانات الحالية
            for item in self.backups_tree.get_children():
                self.backups_tree.delete(item)

            backups = self.backup_service.list_backups()

            for backup in backups:
                self.backups_tree.insert("", tk.END, values=(
                    backup['name'],
                    backup['size_mb'],
                    backup['created_at'],
                    backup.get('description', '')
                ), tags=(backup['path'],))

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل النسخ الاحتياطية: {str(e)}")

    def delete_backup(self):
        """حذف نسخة احتياطية"""
        selection = self.backups_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار نسخة احتياطية للحذف")
            return

        item = self.backups_tree.item(selection[0])
        backup_name = item['values'][0]
        backup_path = self.backups_tree.item(selection[0], "tags")[0]

        if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف النسخة الاحتياطية:\n{backup_name}؟"):
            success, message = self.backup_service.delete_backup(backup_path)

            if success:
                messagebox.showinfo("نجح", message)
                self.load_backups()
            else:
                messagebox.showerror("خطأ", message)

    def verify_backup(self):
        """التحقق من صحة النسخة الاحتياطية"""
        selection = self.backups_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار نسخة احتياطية للتحقق منها")
            return

        backup_path = self.backups_tree.item(selection[0], "tags")[0]

        success, message, info = self.backup_service.verify_backup(backup_path)

        if success:
            details = f"""✅ النسخة الاحتياطية صالحة

📁 حجم الملف: {info['file_size'] / (1024*1024):.2f} MB
💾 يحتوي على قاعدة البيانات: {'نعم' if info['contains_database'] else 'لا'}
⚙️ يحتوي على ملفات التكوين: {'نعم' if info['contains_config'] else 'لا'}

{message}"""
            messagebox.showinfo("نتيجة التحقق", details)
        else:
            messagebox.showerror("خطأ في التحقق", message)

    def show_backup_details(self):
        """عرض تفاصيل النسخة الاحتياطية"""
        selection = self.backups_tree.selection()
        if not selection:
            return

        backup_path = self.backups_tree.item(selection[0], "tags")[0]
        self.verify_backup()  # استخدام نفس دالة التحقق لعرض التفاصيل

    def export_backup_report(self):
        """تصدير تقرير النسخ الاحتياطية"""
        try:
            filename = f"backup_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            success, message = self.backup_service.export_backup_info(filename)

            if success:
                messagebox.showinfo("نجح", message)
            else:
                messagebox.showerror("خطأ", message)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير التقرير: {str(e)}")

    def browse_backup_file(self):
        """تصفح ملف النسخة الاحتياطية"""
        file_path = filedialog.askopenfilename(
            title="اختيار ملف النسخة الاحتياطية",
            filetypes=[("Backup files", "*.zip"), ("All files", "*.*")]
        )

        if file_path:
            self.restore_file_var.set(file_path)
            self.restore_btn.config(state="normal")
            self.restore_status_label.config(text="تم اختيار الملف - يمكنك الآن بدء الاستعادة")

    def verify_selected_backup(self):
        """التحقق من الملف المحدد للاستعادة"""
        file_path = self.restore_file_var.get()
        if not file_path:
            messagebox.showwarning("تحذير", "يرجى اختيار ملف النسخة الاحتياطية أولاً")
            return

        success, message, info = self.backup_service.verify_backup(file_path)

        if success:
            details = f"""✅ الملف صالح للاستعادة

📁 حجم الملف: {info['file_size'] / (1024*1024):.2f} MB
💾 يحتوي على قاعدة البيانات: {'نعم' if info['contains_database'] else 'لا'}
⚙️ يحتوي على ملفات التكوين: {'نعم' if info['contains_config'] else 'لا'}

يمكنك الآن بدء عملية الاستعادة بأمان."""
            messagebox.showinfo("نتيجة التحقق", details)
        else:
            messagebox.showerror("خطأ في الملف", message)
            self.restore_btn.config(state="disabled")

    def restore_backup(self):
        """استعادة النسخة الاحتياطية"""
        file_path = self.restore_file_var.get()
        if not file_path:
            messagebox.showwarning("تحذير", "يرجى اختيار ملف النسخة الاحتياطية أولاً")
            return

        # تأكيد نهائي
        if not messagebox.askyesno("تأكيد الاستعادة",
                                  "⚠️ تحذير: ستتم استعادة النسخة الاحتياطية وسيتم استبدال البيانات الحالية.\n\n"
                                  "هل أنت متأكد من المتابعة؟"):
            return

        def restore_thread():
            try:
                # تحديث الواجهة في الخيط الرئيسي
                self.window.after(0, lambda: self.restore_progress_bar.config(mode='indeterminate'))
                self.window.after(0, lambda: self.restore_progress_bar.start())
                self.window.after(0, lambda: self.restore_status_label.config(text="جاري استعادة النسخة الاحتياطية..."))
                self.window.after(0, lambda: self.restore_btn.config(state="disabled"))

                restore_db = self.restore_database_var.get()
                restore_config = self.restore_config_var.get()

                success, message = self.backup_service.restore_backup(file_path, restore_db, restore_config)

                # تحديث الواجهة في الخيط الرئيسي
                self.window.after(0, lambda: self.restore_progress_bar.stop())
                self.window.after(0, lambda: self.restore_progress_bar.config(mode='determinate'))

                if success:
                    self.window.after(0, lambda: self.restore_status_label.config(text="تمت الاستعادة بنجاح ✅"))
                    self.window.after(0, lambda: messagebox.showinfo("نجح", f"{message}\n\nسيتم إعادة تشغيل التطبيق الآن."))

                    # إعادة تشغيل التطبيق
                    self.window.after(0, lambda: self.window.destroy())
                    self.window.after(0, lambda: self.parent.quit())
                else:
                    self.window.after(0, lambda: self.restore_status_label.config(text="فشلت الاستعادة ❌"))
                    self.window.after(0, lambda: messagebox.showerror("خطأ", message))

            except Exception as e:
                self.window.after(0, lambda: self.restore_progress_bar.stop())
                self.window.after(0, lambda: self.restore_status_label.config(text="خطأ في الاستعادة ❌"))
                self.window.after(0, lambda: messagebox.showerror("خطأ", f"خطأ غير متوقع: {str(e)}"))
            finally:
                self.window.after(0, lambda: self.restore_btn.config(state="normal"))

        threading.Thread(target=restore_thread, daemon=True).start()

    def open_backup_folder(self):
        """فتح مجلد النسخ الاحتياطية"""
        try:
            backup_dir = self.backup_service.backup_dir
            if os.path.exists(backup_dir):
                os.startfile(backup_dir)  # Windows
            else:
                messagebox.showwarning("تحذير", "مجلد النسخ الاحتياطية غير موجود")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح المجلد: {str(e)}")

    def show_help(self):
        """عرض المساعدة"""
        help_text = """مساعدة إدارة النسخ الاحتياطي

🔄 إنشاء نسخة احتياطية:
• أدخل اسماً مميزاً للنسخة الاحتياطية
• اختر ما إذا كنت تريد تضمين الملفات الإضافية
• انقر على "إنشاء نسخة احتياطية"

📋 إدارة النسخ الاحتياطية:
• عرض جميع النسخ الاحتياطية المتاحة
• حذف النسخ القديمة أو غير المرغوبة
• التحقق من صحة النسخ الاحتياطية
• تصدير تقرير بجميع النسخ

🔄 الاستعادة:
• اختر ملف النسخة الاحتياطية
• تحقق من صحة الملف قبل الاستعادة
• اختر ما تريد استعادته (قاعدة البيانات، التكوين)
• بدء عملية الاستعادة

⚠️ نصائح مهمة:
• أنشئ نسخة احتياطية قبل أي تغييرات مهمة
• احتفظ بنسخ متعددة في أماكن مختلفة
• تحقق من النسخ الاحتياطية دورياً
• لا تحذف جميع النسخ الاحتياطية مرة واحدة"""

        messagebox.showinfo("مساعدة", help_text)

if __name__ == "__main__":
    # للاختبار
    root = tk.Tk()
    root.withdraw()

    from src.services.auth_service import AuthService
    auth = AuthService()
    auth.create_default_admin()
    success, message, user = auth.login("admin", "admin123")

    if success:
        app = BackupWindow(root, user)
        root.mainloop()
