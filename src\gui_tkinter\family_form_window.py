"""
نافذة إضافة/تعديل الأسرة المتقدمة
Advanced Family Add/Edit Window
"""
import tkinter as tk
from tkinter import ttk, messagebox
from datetime import date, datetime
import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.services.family_service import FamilyService
from src.utils.validators import validate_name, validate_phone_number, validate_date

class FamilyFormWindow:
    """نافذة إضافة/تعديل الأسرة المتقدمة"""
    
    def __init__(self, parent, current_user, family_id=None):
        self.parent = parent
        self.current_user = current_user
        self.family_id = family_id
        self.family_service = FamilyService()
        self.family_data = None
        self.individuals_data = []
        
        # تحديد نوع العملية
        self.is_edit_mode = family_id is not None
        
        self.window = tk.Toplevel(parent)
        self.setup_window()
        self.create_widgets()
        
        if self.is_edit_mode:
            self.load_family_data()
        
    def setup_window(self):
        """إعداد النافذة"""
        title = "تعديل بيانات الأسرة" if self.is_edit_mode else "إضافة أسرة جديدة"
        self.window.title(title)
        self.window.geometry("800x700")
        self.window.resizable(True, True)
        
        # توسيط النافذة
        self.center_window()
        
        # جعل النافذة modal
        self.window.transient(self.parent)
        self.window.grab_set()
        
    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = 800
        height = 700
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
        
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان
        title_text = "تعديل بيانات الأسرة" if self.is_edit_mode else "إضافة أسرة جديدة"
        title_label = ttk.Label(self.window, text=title_text, 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # إنشاء التبويبات
        self.notebook = ttk.Notebook(self.window)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # تبويب بيانات رب الأسرة
        self.create_head_tab()
        
        # تبويب أفراد الأسرة
        self.create_individuals_tab()
        
        # تبويب معلومات إضافية
        self.create_additional_info_tab()
        
        # أزرار التحكم
        self.create_control_buttons()
        
    def create_head_tab(self):
        """إنشاء تبويب بيانات رب الأسرة"""
        head_frame = ttk.Frame(self.notebook)
        self.notebook.add(head_frame, text="بيانات رب الأسرة")
        
        # إطار التمرير
        canvas = tk.Canvas(head_frame)
        scrollbar = ttk.Scrollbar(head_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط التمرير
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # الحقول
        main_frame = ttk.Frame(scrollable_frame, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # رقم الأسرة (للعرض فقط في حالة التعديل)
        if self.is_edit_mode:
            ttk.Label(main_frame, text="رقم الأسرة:", font=("Arial", 10, "bold")).pack(anchor=tk.W, pady=(0, 5))
            self.family_id_label = ttk.Label(main_frame, text="", font=("Arial", 10))
            self.family_id_label.pack(anchor=tk.W, pady=(0, 15))
        
        # اسم رب الأسرة *
        ttk.Label(main_frame, text="اسم رب الأسرة *:", font=("Arial", 10, "bold")).pack(anchor=tk.W, pady=(0, 5))
        self.head_name_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.head_name_var, width=50).pack(fill=tk.X, pady=(0, 15))
        
        # تاريخ الميلاد *
        ttk.Label(main_frame, text="تاريخ الميلاد *:", font=("Arial", 10, "bold")).pack(anchor=tk.W, pady=(0, 5))
        birth_frame = ttk.Frame(main_frame)
        birth_frame.pack(fill=tk.X, pady=(0, 15))
        
        self.birth_day_var = tk.StringVar()
        self.birth_month_var = tk.StringVar()
        self.birth_year_var = tk.StringVar()
        
        ttk.Label(birth_frame, text="اليوم:").pack(side=tk.LEFT)
        day_combo = ttk.Combobox(birth_frame, textvariable=self.birth_day_var, width=5, state="readonly")
        day_combo['values'] = [str(i) for i in range(1, 32)]
        day_combo.pack(side=tk.LEFT, padx=(5, 10))
        
        ttk.Label(birth_frame, text="الشهر:").pack(side=tk.LEFT)
        month_combo = ttk.Combobox(birth_frame, textvariable=self.birth_month_var, width=5, state="readonly")
        month_combo['values'] = [str(i) for i in range(1, 13)]
        month_combo.pack(side=tk.LEFT, padx=(5, 10))
        
        ttk.Label(birth_frame, text="السنة:").pack(side=tk.LEFT)
        year_combo = ttk.Combobox(birth_frame, textvariable=self.birth_year_var, width=8, state="readonly")
        current_year = datetime.now().year
        year_combo['values'] = [str(i) for i in range(1920, current_year + 1)]
        year_combo.pack(side=tk.LEFT, padx=(5, 0))
        
        # الجنس *
        ttk.Label(main_frame, text="الجنس *:", font=("Arial", 10, "bold")).pack(anchor=tk.W, pady=(0, 5))
        self.gender_var = tk.StringVar()
        gender_frame = ttk.Frame(main_frame)
        gender_frame.pack(fill=tk.X, pady=(0, 15))
        
        ttk.Radiobutton(gender_frame, text="ذكر", variable=self.gender_var, value="ذكر").pack(side=tk.LEFT, padx=(0, 20))
        ttk.Radiobutton(gender_frame, text="أنثى", variable=self.gender_var, value="أنثى").pack(side=tk.LEFT)
        
        # الحالة الاجتماعية *
        ttk.Label(main_frame, text="الحالة الاجتماعية *:", font=("Arial", 10, "bold")).pack(anchor=tk.W, pady=(0, 5))
        self.marital_status_var = tk.StringVar()
        marital_combo = ttk.Combobox(main_frame, textvariable=self.marital_status_var, width=47, state="readonly")
        marital_combo['values'] = ["أعزب", "متزوج", "مطلق", "أرمل"]
        marital_combo.pack(fill=tk.X, pady=(0, 15))
        
        # رقم الهاتف
        ttk.Label(main_frame, text="رقم الهاتف:", font=("Arial", 10, "bold")).pack(anchor=tk.W, pady=(0, 5))
        self.phone_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.phone_var, width=50).pack(fill=tk.X, pady=(0, 15))
        
        # وثيقة إثبات الهوية
        ttk.Label(main_frame, text="رقم وثيقة الهوية:", font=("Arial", 10, "bold")).pack(anchor=tk.W, pady=(0, 5))
        self.id_document_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.id_document_var, width=50).pack(fill=tk.X, pady=(0, 15))
        
        # الجنسية
        ttk.Label(main_frame, text="الجنسية:", font=("Arial", 10, "bold")).pack(anchor=tk.W, pady=(0, 5))
        self.nationality_var = tk.StringVar()
        nationality_combo = ttk.Combobox(main_frame, textvariable=self.nationality_var, width=47)
        nationality_combo['values'] = ["فلسطيني", "سوري", "لبناني", "أردني", "عراقي", "يمني", "أخرى"]
        nationality_combo.pack(fill=tk.X, pady=(0, 15))
        
        # ملاحظة
        ttk.Label(main_frame, text="* حقول مطلوبة", font=("Arial", 9), foreground="red").pack(anchor=tk.W, pady=(10, 0))
        
    def create_individuals_tab(self):
        """إنشاء تبويب أفراد الأسرة"""
        individuals_frame = ttk.Frame(self.notebook)
        self.notebook.add(individuals_frame, text="أفراد الأسرة")
        
        # شريط الأدوات
        toolbar_frame = ttk.Frame(individuals_frame)
        toolbar_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(toolbar_frame, text="إضافة فرد", 
                  command=self.add_individual).pack(side=tk.LEFT, padx=5)
        ttk.Button(toolbar_frame, text="تعديل فرد", 
                  command=self.edit_individual).pack(side=tk.LEFT, padx=5)
        ttk.Button(toolbar_frame, text="حذف فرد", 
                  command=self.delete_individual).pack(side=tk.LEFT, padx=5)
        
        # جدول الأفراد
        self.create_individuals_table(individuals_frame)
        
    def create_individuals_table(self, parent):
        """إنشاء جدول أفراد الأسرة"""
        # إطار الجدول
        table_frame = ttk.Frame(parent)
        table_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # أعمدة الجدول
        columns = ("name", "birth_date", "gender", "relationship")
        
        self.individuals_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=10)
        
        # تعيين عناوين الأعمدة
        self.individuals_tree.heading("name", text="الاسم الكامل")
        self.individuals_tree.heading("birth_date", text="تاريخ الميلاد")
        self.individuals_tree.heading("gender", text="الجنس")
        self.individuals_tree.heading("relationship", text="صلة القرابة")
        
        # تعيين عرض الأعمدة
        self.individuals_tree.column("name", width=200)
        self.individuals_tree.column("birth_date", width=120)
        self.individuals_tree.column("gender", width=80)
        self.individuals_tree.column("relationship", width=120)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.individuals_tree.yview)
        self.individuals_tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.individuals_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط النقر المزدوج
        self.individuals_tree.bind("<Double-1>", lambda e: self.edit_individual())
        
    def create_additional_info_tab(self):
        """إنشاء تبويب المعلومات الإضافية"""
        additional_frame = ttk.Frame(self.notebook)
        self.notebook.add(additional_frame, text="معلومات إضافية")
        
        main_frame = ttk.Frame(additional_frame, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # تاريخ الوصول
        ttk.Label(main_frame, text="تاريخ الوصول للمخيم:", font=("Arial", 10, "bold")).pack(anchor=tk.W, pady=(0, 5))
        arrival_frame = ttk.Frame(main_frame)
        arrival_frame.pack(fill=tk.X, pady=(0, 15))
        
        self.arrival_day_var = tk.StringVar()
        self.arrival_month_var = tk.StringVar()
        self.arrival_year_var = tk.StringVar()
        
        # تعيين التاريخ الحالي كافتراضي
        today = date.today()
        self.arrival_day_var.set(str(today.day))
        self.arrival_month_var.set(str(today.month))
        self.arrival_year_var.set(str(today.year))
        
        ttk.Label(arrival_frame, text="اليوم:").pack(side=tk.LEFT)
        arrival_day_combo = ttk.Combobox(arrival_frame, textvariable=self.arrival_day_var, width=5, state="readonly")
        arrival_day_combo['values'] = [str(i) for i in range(1, 32)]
        arrival_day_combo.pack(side=tk.LEFT, padx=(5, 10))
        
        ttk.Label(arrival_frame, text="الشهر:").pack(side=tk.LEFT)
        arrival_month_combo = ttk.Combobox(arrival_frame, textvariable=self.arrival_month_var, width=5, state="readonly")
        arrival_month_combo['values'] = [str(i) for i in range(1, 13)]
        arrival_month_combo.pack(side=tk.LEFT, padx=(5, 10))
        
        ttk.Label(arrival_frame, text="السنة:").pack(side=tk.LEFT)
        arrival_year_combo = ttk.Combobox(arrival_frame, textvariable=self.arrival_year_var, width=8, state="readonly")
        current_year = datetime.now().year
        arrival_year_combo['values'] = [str(i) for i in range(2020, current_year + 1)]
        arrival_year_combo.pack(side=tk.LEFT, padx=(5, 0))
        
        # رقم الخيمة/المأوى
        ttk.Label(main_frame, text="رقم الخيمة/المأوى:", font=("Arial", 10, "bold")).pack(anchor=tk.W, pady=(0, 5))
        self.tent_number_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.tent_number_var, width=50).pack(fill=tk.X, pady=(0, 15))
        
        # بلد المنشأ
        ttk.Label(main_frame, text="بلد المنشأ:", font=("Arial", 10, "bold")).pack(anchor=tk.W, pady=(0, 5))
        self.origin_country_var = tk.StringVar()
        country_combo = ttk.Combobox(main_frame, textvariable=self.origin_country_var, width=47)
        country_combo['values'] = ["فلسطين", "سوريا", "لبنان", "الأردن", "العراق", "اليمن", "أخرى"]
        country_combo.pack(fill=tk.X, pady=(0, 15))
        
        # مدينة المنشأ
        ttk.Label(main_frame, text="مدينة المنشأ:", font=("Arial", 10, "bold")).pack(anchor=tk.W, pady=(0, 5))
        self.origin_city_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.origin_city_var, width=50).pack(fill=tk.X, pady=(0, 15))
        
        # ملاحظات
        ttk.Label(main_frame, text="ملاحظات:", font=("Arial", 10, "bold")).pack(anchor=tk.W, pady=(0, 5))
        self.notes_text = tk.Text(main_frame, height=5, width=50)
        self.notes_text.pack(fill=tk.X, pady=(0, 15))
        
    def create_control_buttons(self):
        """إنشاء أزرار التحكم"""
        buttons_frame = ttk.Frame(self.window)
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # أزرار اليمين
        right_buttons = ttk.Frame(buttons_frame)
        right_buttons.pack(side=tk.RIGHT)
        
        ttk.Button(right_buttons, text="إلغاء", 
                  command=self.window.destroy).pack(side=tk.RIGHT, padx=(10, 0))
        
        save_text = "حفظ التعديلات" if self.is_edit_mode else "حفظ الأسرة"
        ttk.Button(right_buttons, text=save_text, 
                  command=self.save_family).pack(side=tk.RIGHT)
        
        # أزرار اليسار
        if self.is_edit_mode:
            left_buttons = ttk.Frame(buttons_frame)
            left_buttons.pack(side=tk.LEFT)
            
            ttk.Button(left_buttons, text="حذف الأسرة", 
                      command=self.delete_family).pack(side=tk.LEFT)
    
    # الدوال المساعدة (سأضيفها في الجزء التالي)
    def load_family_data(self):
        """تحميل بيانات الأسرة للتعديل"""
        messagebox.showinfo("قيد التطوير", "ميزة تحميل بيانات الأسرة قيد التطوير")
    
    def add_individual(self):
        """إضافة فرد جديد"""
        messagebox.showinfo("قيد التطوير", "ميزة إضافة فرد قيد التطوير")
    
    def edit_individual(self):
        """تعديل فرد"""
        messagebox.showinfo("قيد التطوير", "ميزة تعديل فرد قيد التطوير")
    
    def delete_individual(self):
        """حذف فرد"""
        messagebox.showinfo("قيد التطوير", "ميزة حذف فرد قيد التطوير")
    
    def save_family(self):
        """حفظ بيانات الأسرة"""
        if self.validate_form():
            messagebox.showinfo("قيد التطوير", "ميزة حفظ الأسرة قيد التطوير")
    
    def delete_family(self):
        """حذف الأسرة"""
        if messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف هذه الأسرة؟"):
            messagebox.showinfo("قيد التطوير", "ميزة حذف الأسرة قيد التطوير")
    
    def validate_form(self):
        """التحقق من صحة البيانات"""
        # التحقق من الحقول المطلوبة
        if not self.head_name_var.get().strip():
            messagebox.showerror("خطأ", "اسم رب الأسرة مطلوب")
            return False
        
        if not self.gender_var.get():
            messagebox.showerror("خطأ", "يرجى اختيار الجنس")
            return False
        
        if not self.marital_status_var.get():
            messagebox.showerror("خطأ", "يرجى اختيار الحالة الاجتماعية")
            return False
        
        # التحقق من تاريخ الميلاد
        if not (self.birth_day_var.get() and self.birth_month_var.get() and self.birth_year_var.get()):
            messagebox.showerror("خطأ", "تاريخ الميلاد مطلوب")
            return False
        
        # التحقق من صحة رقم الهاتف
        phone = self.phone_var.get().strip()
        if phone:
            valid, error = validate_phone_number(phone)
            if not valid:
                messagebox.showerror("خطأ", f"رقم الهاتف غير صحيح: {error}")
                return False
        
        return True

if __name__ == "__main__":
    # للاختبار
    root = tk.Tk()
    root.withdraw()  # إخفاء النافذة الرئيسية
    
    from src.services.auth_service import AuthService
    auth = AuthService()
    auth.create_default_admin()
    success, message, user = auth.login("admin", "admin123")
    
    if success:
        app = FamilyFormWindow(root, user)
        root.mainloop()
