"""
نافذة استيراد البيانات من Excel
Excel Import Window
"""
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import pandas as pd
import sys
import os
from datetime import datetime

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.services.family_service import FamilyService
from src.services.excel_service import ExcelService

class ImportExcelWindow:
    """نافذة استيراد البيانات من Excel"""

    def __init__(self, parent, current_user):
        self.parent = parent
        self.current_user = current_user
        self.family_service = FamilyService()
        self.excel_service = ExcelService()

        self.file_path = None
        self.data_frame = None
        self.validation_results = []

        self.window = tk.Toplevel(parent)
        self.setup_window()
        self.create_widgets()

    def setup_window(self):
        """إعداد النافذة"""
        self.window.title("استيراد البيانات من Excel")
        self.window.geometry("900x600")
        self.window.resizable(True, True)

        # توسيط النافذة
        self.center_window()

        # جعل النافذة modal
        self.window.transient(self.parent)
        self.window.grab_set()

    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = 900
        height = 600
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان
        title_label = ttk.Label(self.window, text="استيراد البيانات من Excel",
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=10)

        # إنشاء التبويبات
        self.notebook = ttk.Notebook(self.window)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # تبويب اختيار الملف
        self.create_file_selection_tab()

        # تبويب معاينة البيانات
        self.create_data_preview_tab()

        # تبويب التحقق من البيانات
        self.create_validation_tab()

        # تبويب الاستيراد
        self.create_import_tab()

        # أزرار التحكم
        self.create_control_buttons()

    def create_file_selection_tab(self):
        """إنشاء تبويب اختيار الملف"""
        file_frame = ttk.Frame(self.notebook)
        self.notebook.add(file_frame, text="اختيار الملف")

        main_frame = ttk.Frame(file_frame, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # معلومات
        info_frame = ttk.LabelFrame(main_frame, text="معلومات مهمة", padding="10")
        info_frame.pack(fill=tk.X, pady=(0, 20))

        info_text = """تعليمات الاستيراد:

1. يجب أن يكون الملف بصيغة Excel (.xlsx)
2. يجب أن تحتوي الورقة الأولى على البيانات
3. الصف الأول يجب أن يحتوي على عناوين الأعمدة
4. الأعمدة المطلوبة: اسم رب الأسرة، الجنس، تاريخ الميلاد
5. يمكن تحميل قالب Excel من التبويب التالي"""

        ttk.Label(info_frame, text=info_text, font=("Arial", 10),
                 justify=tk.LEFT).pack(anchor=tk.W)

        # اختيار الملف
        file_selection_frame = ttk.LabelFrame(main_frame, text="اختيار ملف Excel", padding="10")
        file_selection_frame.pack(fill=tk.X, pady=(0, 20))

        self.file_path_var = tk.StringVar()
        file_entry = ttk.Entry(file_selection_frame, textvariable=self.file_path_var,
                              width=60, state="readonly")
        file_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))

        ttk.Button(file_selection_frame, text="تصفح",
                  command=self.browse_file).pack(side=tk.RIGHT)

        # تحميل القالب
        template_frame = ttk.LabelFrame(main_frame, text="تحميل قالب Excel", padding="10")
        template_frame.pack(fill=tk.X, pady=(0, 20))

        template_text = "يمكنك تحميل قالب Excel جاهز لتعبئة البيانات"
        ttk.Label(template_frame, text=template_text).pack(anchor=tk.W, pady=(0, 10))

        ttk.Button(template_frame, text="تحميل قالب الأسر",
                  command=self.download_families_template).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(template_frame, text="تحميل قالب الأفراد",
                  command=self.download_individuals_template).pack(side=tk.LEFT)

        # معاينة الملف
        preview_frame = ttk.LabelFrame(main_frame, text="معاينة سريعة", padding="10")
        preview_frame.pack(fill=tk.BOTH, expand=True)

        self.file_info_text = tk.Text(preview_frame, height=8, state="disabled")
        self.file_info_text.pack(fill=tk.BOTH, expand=True)

    def create_data_preview_tab(self):
        """إنشاء تبويب معاينة البيانات"""
        preview_frame = ttk.Frame(self.notebook)
        self.notebook.add(preview_frame, text="معاينة البيانات")

        # شريط الأدوات
        toolbar_frame = ttk.Frame(preview_frame)
        toolbar_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(toolbar_frame, text="تحديث المعاينة",
                  command=self.update_preview).pack(side=tk.LEFT, padx=5)

        self.rows_label = ttk.Label(toolbar_frame, text="عدد الصفوف: 0")
        self.rows_label.pack(side=tk.RIGHT)

        # جدول المعاينة
        self.preview_tree = ttk.Treeview(preview_frame, show="headings", height=15)

        # شريط التمرير
        preview_scrollbar = ttk.Scrollbar(preview_frame, orient=tk.VERTICAL,
                                         command=self.preview_tree.yview)
        self.preview_tree.configure(yscrollcommand=preview_scrollbar.set)

        # تخطيط الجدول
        self.preview_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 0), pady=5)
        preview_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, padx=(0, 10), pady=5)

    def create_validation_tab(self):
        """إنشاء تبويب التحقق من البيانات"""
        validation_frame = ttk.Frame(self.notebook)
        self.notebook.add(validation_frame, text="التحقق من البيانات")

        # شريط الأدوات
        validation_toolbar = ttk.Frame(validation_frame)
        validation_toolbar.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(validation_toolbar, text="بدء التحقق",
                  command=self.validate_data).pack(side=tk.LEFT, padx=5)

        self.validation_status_label = ttk.Label(validation_toolbar, text="لم يتم التحقق بعد")
        self.validation_status_label.pack(side=tk.RIGHT)

        # نتائج التحقق
        self.validation_tree = ttk.Treeview(validation_frame,
                                           columns=("row", "column", "error", "value"),
                                           show="headings", height=15)

        self.validation_tree.heading("row", text="الصف")
        self.validation_tree.heading("column", text="العمود")
        self.validation_tree.heading("error", text="نوع الخطأ")
        self.validation_tree.heading("value", text="القيمة")

        self.validation_tree.column("row", width=80)
        self.validation_tree.column("column", width=150)
        self.validation_tree.column("error", width=200)
        self.validation_tree.column("value", width=150)

        # شريط التمرير للتحقق
        validation_scrollbar = ttk.Scrollbar(validation_frame, orient=tk.VERTICAL,
                                           command=self.validation_tree.yview)
        self.validation_tree.configure(yscrollcommand=validation_scrollbar.set)

        # تخطيط جدول التحقق
        self.validation_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 0), pady=5)
        validation_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, padx=(0, 10), pady=5)

    def create_import_tab(self):
        """إنشاء تبويب الاستيراد"""
        import_frame = ttk.Frame(self.notebook)
        self.notebook.add(import_frame, text="الاستيراد")

        main_frame = ttk.Frame(import_frame, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # ملخص الاستيراد
        summary_frame = ttk.LabelFrame(main_frame, text="ملخص الاستيراد", padding="10")
        summary_frame.pack(fill=tk.X, pady=(0, 20))

        self.summary_text = tk.Text(summary_frame, height=8, state="disabled")
        self.summary_text.pack(fill=tk.BOTH, expand=True)

        # خيارات الاستيراد
        options_frame = ttk.LabelFrame(main_frame, text="خيارات الاستيراد", padding="10")
        options_frame.pack(fill=tk.X, pady=(0, 20))

        self.skip_errors_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="تخطي الصفوف التي تحتوي على أخطاء",
                       variable=self.skip_errors_var).pack(anchor=tk.W)

        self.create_backup_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="إنشاء نسخة احتياطية قبل الاستيراد",
                       variable=self.create_backup_var).pack(anchor=tk.W)

        # شريط التقدم
        progress_frame = ttk.LabelFrame(main_frame, text="تقدم الاستيراد", padding="10")
        progress_frame.pack(fill=tk.X, pady=(0, 20))

        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var,
                                           maximum=100)
        self.progress_bar.pack(fill=tk.X, pady=(0, 10))

        self.progress_label = ttk.Label(progress_frame, text="جاهز للاستيراد")
        self.progress_label.pack()

        # أزرار الاستيراد
        import_buttons_frame = ttk.Frame(main_frame)
        import_buttons_frame.pack(fill=tk.X)

        ttk.Button(import_buttons_frame, text="بدء الاستيراد",
                  command=self.start_import).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(import_buttons_frame, text="إيقاف الاستيراد",
                  command=self.stop_import, state="disabled").pack(side=tk.LEFT)

    def create_control_buttons(self):
        """إنشاء أزرار التحكم"""
        buttons_frame = ttk.Frame(self.window)
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Button(buttons_frame, text="إغلاق",
                  command=self.window.destroy).pack(side=tk.RIGHT, padx=5)

        ttk.Button(buttons_frame, text="مساعدة",
                  command=self.show_help).pack(side=tk.LEFT, padx=5)

    def browse_file(self):
        """تصفح واختيار ملف Excel"""
        file_path = filedialog.askopenfilename(
            title="اختيار ملف Excel",
            filetypes=[("Excel files", "*.xlsx *.xls"), ("All files", "*.*")]
        )

        if file_path:
            self.file_path = file_path
            self.file_path_var.set(file_path)
            self.load_file_info()

    def load_file_info(self):
        """تحميل معلومات الملف"""
        if not self.file_path:
            return

        try:
            # قراءة الملف
            self.data_frame = pd.read_excel(self.file_path)

            # عرض معلومات الملف
            info_text = f"""معلومات الملف:
الملف: {os.path.basename(self.file_path)}
عدد الصفوف: {len(self.data_frame)}
عدد الأعمدة: {len(self.data_frame.columns)}

الأعمدة الموجودة:
{', '.join(self.data_frame.columns.tolist())}

أول 5 صفوف:
{self.data_frame.head().to_string()}"""

            self.file_info_text.config(state="normal")
            self.file_info_text.delete(1.0, tk.END)
            self.file_info_text.insert(1.0, info_text)
            self.file_info_text.config(state="disabled")

            # تحديث المعاينة
            self.update_preview()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في قراءة الملف: {str(e)}")

    def update_preview(self):
        """تحديث معاينة البيانات"""
        if self.data_frame is None:
            return

        try:
            # مسح البيانات الحالية
            for item in self.preview_tree.get_children():
                self.preview_tree.delete(item)

            # تعيين الأعمدة
            columns = list(self.data_frame.columns)
            self.preview_tree["columns"] = columns

            for col in columns:
                self.preview_tree.heading(col, text=col)
                self.preview_tree.column(col, width=150)

            # إضافة البيانات (أول 100 صف)
            for index, row in self.data_frame.head(100).iterrows():
                values = [str(row[col]) if pd.notna(row[col]) else "" for col in columns]
                self.preview_tree.insert("", tk.END, values=values)

            # تحديث عدد الصفوف
            self.rows_label.config(text=f"عدد الصفوف: {len(self.data_frame)}")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحديث المعاينة: {str(e)}")

    def validate_data(self):
        """التحقق من صحة البيانات"""
        if self.data_frame is None:
            messagebox.showwarning("تحذير", "يرجى اختيار ملف أولاً")
            return

        self.validation_results = []

        # مسح نتائج التحقق السابقة
        for item in self.validation_tree.get_children():
            self.validation_tree.delete(item)

        try:
            # التحقق من الأعمدة المطلوبة
            required_columns = ['اسم رب الأسرة', 'الجنس']
            missing_columns = [col for col in required_columns if col not in self.data_frame.columns]

            if missing_columns:
                for col in missing_columns:
                    error = {
                        'row': 'عام',
                        'column': col,
                        'error': 'عمود مطلوب مفقود',
                        'value': ''
                    }
                    self.validation_results.append(error)

            # التحقق من البيانات في كل صف
            for index, row in self.data_frame.iterrows():
                row_number = index + 2  # +2 لأن الصف الأول عناوين والفهرس يبدأ من 0

                # التحقق من اسم رب الأسرة
                if 'اسم رب الأسرة' in self.data_frame.columns:
                    name = row['اسم رب الأسرة']
                    if pd.isna(name) or str(name).strip() == '':
                        error = {
                            'row': str(row_number),
                            'column': 'اسم رب الأسرة',
                            'error': 'اسم رب الأسرة مطلوب',
                            'value': str(name) if not pd.isna(name) else ''
                        }
                        self.validation_results.append(error)

                # التحقق من الجنس
                if 'الجنس' in self.data_frame.columns:
                    gender = row['الجنس']
                    if not pd.isna(gender) and str(gender) not in ['ذكر', 'أنثى']:
                        error = {
                            'row': str(row_number),
                            'column': 'الجنس',
                            'error': 'قيمة غير صحيحة (يجب أن تكون ذكر أو أنثى)',
                            'value': str(gender)
                        }
                        self.validation_results.append(error)

            # عرض نتائج التحقق
            for error in self.validation_results:
                self.validation_tree.insert("", tk.END, values=(
                    error['row'],
                    error['column'],
                    error['error'],
                    error['value']
                ))

            # تحديث حالة التحقق
            if self.validation_results:
                self.validation_status_label.config(
                    text=f"تم العثور على {len(self.validation_results)} خطأ",
                    foreground="red"
                )
            else:
                self.validation_status_label.config(
                    text="البيانات صحيحة ✓",
                    foreground="green"
                )

            # تحديث ملخص الاستيراد
            self.update_import_summary()

            # تعيين علامة اكتمال التحقق
            self.validation_completed = True

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في التحقق من البيانات: {str(e)}")
            self.validation_completed = False

    def update_import_summary(self):
        """تحديث ملخص الاستيراد"""
        if self.data_frame is None:
            return

        total_rows = len(self.data_frame)
        error_rows = len(set(error['row'] for error in self.validation_results if error['row'] != 'عام'))
        valid_rows = total_rows - error_rows

        summary = f"""ملخص الاستيراد:

إجمالي الصفوف: {total_rows}
الصفوف الصحيحة: {valid_rows}
الصفوف التي تحتوي على أخطاء: {error_rows}
إجمالي الأخطاء: {len(self.validation_results)}

سيتم استيراد: {valid_rows if self.skip_errors_var.get() else 0} أسرة

ملاحظة: يمكنك اختيار تخطي الصفوف التي تحتوي على أخطاء أو إصلاحها في الملف أولاً."""

        self.summary_text.config(state="normal")
        self.summary_text.delete(1.0, tk.END)
        self.summary_text.insert(1.0, summary)
        self.summary_text.config(state="disabled")

    def start_import(self):
        """بدء عملية الاستيراد"""
        if self.data_frame is None:
            messagebox.showwarning("تحذير", "يرجى اختيار ملف أولاً")
            return

        # التحقق من أن التحقق تم تشغيله
        if not hasattr(self, 'validation_completed') or not self.validation_completed:
            messagebox.showwarning("تحذير", "يرجى التحقق من البيانات أولاً")
            return

        if messagebox.askyesno("تأكيد الاستيراد", "هل أنت متأكد من بدء عملية الاستيراد؟"):
            self.perform_import()

    def perform_import(self):
        """تنفيذ عملية الاستيراد"""
        try:
            self.progress_var.set(0)
            self.progress_label.config(text="جاري الاستيراد...")
            self.window.update()

            success_count = 0
            error_count = 0
            total_rows = len(self.data_frame)

            for index, row in self.data_frame.iterrows():
                try:
                    # تحديث شريط التقدم
                    progress = ((index + 1) / total_rows) * 100
                    self.progress_var.set(progress)
                    self.progress_label.config(text=f"معالجة الصف {index + 1} من {total_rows}")
                    self.window.update()

                    # التحقق من البيانات الأساسية
                    if pd.isna(row.get('اسم رب الأسرة')) or str(row.get('اسم رب الأسرة')).strip() == '':
                        if self.skip_errors_var.get():
                            error_count += 1
                            continue
                        else:
                            raise ValueError(f"اسم رب الأسرة مطلوب في الصف {index + 2}")

                    # تحضير بيانات الأسرة
                    family_data = {
                        'head_of_family_name': str(row.get('اسم رب الأسرة')).strip(),
                        'head_gender': str(row.get('الجنس', '')).strip() if not pd.isna(row.get('الجنس')) else None,
                        'phone_number': str(row.get('رقم الهاتف', '')).strip() if not pd.isna(row.get('رقم الهاتف')) else None,
                        'tent_number': str(row.get('رقم الخيمة', '')).strip() if not pd.isna(row.get('رقم الخيمة')) else None,
                        'nationality': str(row.get('الجنسية', '')).strip() if not pd.isna(row.get('الجنسية')) else None,
                        'origin_country': str(row.get('بلد المنشأ', '')).strip() if not pd.isna(row.get('بلد المنشأ')) else None,
                        'origin_city': str(row.get('مدينة المنشأ', '')).strip() if not pd.isna(row.get('مدينة المنشأ')) else None,
                        'notes': str(row.get('ملاحظات', '')).strip() if not pd.isna(row.get('ملاحظات')) else None,
                    }

                    # معالجة تاريخ الميلاد
                    if not pd.isna(row.get('تاريخ الميلاد')):
                        try:
                            birth_date = pd.to_datetime(row.get('تاريخ الميلاد')).date()
                            family_data['head_birth_date'] = birth_date
                        except:
                            pass

                    # معالجة تاريخ الوصول
                    if not pd.isna(row.get('تاريخ الوصول')):
                        try:
                            arrival_date = pd.to_datetime(row.get('تاريخ الوصول')).date()
                            family_data['arrival_date'] = arrival_date
                        except:
                            family_data['arrival_date'] = datetime.now().date()
                    else:
                        family_data['arrival_date'] = datetime.now().date()

                    # إنشاء الأسرة
                    success, message, family = self.family_service.create_family(family_data)

                    if success:
                        success_count += 1
                    else:
                        if self.skip_errors_var.get():
                            error_count += 1
                        else:
                            raise ValueError(f"فشل في إنشاء الأسرة: {message}")

                except Exception as e:
                    if self.skip_errors_var.get():
                        error_count += 1
                    else:
                        raise e

            # إكمال العملية
            self.progress_var.set(100)
            self.progress_label.config(text="تم الانتهاء من الاستيراد")

            # عرض النتائج
            result_message = f"""تم الانتهاء من عملية الاستيراد:

✅ تم استيراد {success_count} أسرة بنجاح
❌ فشل في استيراد {error_count} أسرة
📊 إجمالي الصفوف المعالجة: {total_rows}"""

            messagebox.showinfo("نتائج الاستيراد", result_message)

        except Exception as e:
            messagebox.showerror("خطأ في الاستيراد", f"فشل في عملية الاستيراد: {str(e)}")
            self.progress_label.config(text="فشل في الاستيراد")

    def stop_import(self):
        """إيقاف عملية الاستيراد"""
        messagebox.showinfo("قيد التطوير", "ميزة إيقاف الاستيراد قيد التطوير")

    def download_families_template(self):
        """تحميل قالب الأسر"""
        try:
            filename = f"families_template_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            success = self.excel_service.create_families_template(filename)

            if success:
                messagebox.showinfo("نجح", f"تم إنشاء قالب الأسر: {filename}")
            else:
                messagebox.showerror("خطأ", "فشل في إنشاء القالب")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل القالب: {str(e)}")

    def download_individuals_template(self):
        """تحميل قالب الأفراد"""
        try:
            filename = f"individuals_template_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            success = self.excel_service.create_individuals_template(filename)

            if success:
                messagebox.showinfo("نجح", f"تم إنشاء قالب الأفراد: {filename}")
            else:
                messagebox.showerror("خطأ", "فشل في إنشاء القالب")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل القالب: {str(e)}")

    def show_help(self):
        """عرض المساعدة"""
        help_text = """مساعدة استيراد البيانات من Excel

1. اختيار الملف:
   - اختر ملف Excel (.xlsx)
   - تأكد من وجود البيانات في الورقة الأولى
   - الصف الأول يجب أن يحتوي على عناوين الأعمدة

2. الأعمدة المطلوبة:
   - اسم رب الأسرة (مطلوب)
   - الجنس (مطلوب: ذكر أو أنثى)
   - تاريخ الميلاد (اختياري)
   - رقم الهاتف (اختياري)

3. التحقق من البيانات:
   - سيتم فحص جميع البيانات
   - ستظهر الأخطاء في تبويب التحقق
   - يمكن تخطي الصفوف التي تحتوي على أخطاء

4. الاستيراد:
   - راجع الملخص قبل البدء
   - يمكن إنشاء نسخة احتياطية
   - ستظهر نتائج الاستيراد"""

        messagebox.showinfo("مساعدة", help_text)

if __name__ == "__main__":
    # للاختبار
    root = tk.Tk()
    root.withdraw()

    from src.services.auth_service import AuthService
    auth = AuthService()
    auth.create_default_admin()
    success, message, user = auth.login("admin", "admin123")

    if success:
        app = ImportExcelWindow(root, user)
        root.mainloop()
