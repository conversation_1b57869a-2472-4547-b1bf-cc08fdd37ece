"""
نافذة تسجيل الدخول باستخدام Tkinter
Login Window using Tkinter
"""
import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.services.auth_service import AuthService

class LoginWindow:
    """نافذة تسجيل الدخول"""

    def __init__(self):
        self.root = tk.Tk()
        self.auth_service = AuthService()
        self.current_user = None
        self.setup_window()
        self.create_widgets()

    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title("تسجيل الدخول - نظام إدارة المخيم")
        self.root.geometry("400x500")
        self.root.resizable(False, False)

        # توسيط النافذة
        self.center_window()

        # تعيين الأيقونة (إذا توفرت)
        try:
            self.root.iconbitmap("resources/icons/app.ico")
        except:
            pass

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # إطار رئيسي
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # تكوين الشبكة
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)

        # العنوان
        title_label = ttk.Label(main_frame, text="نظام إدارة المخيم",
                               font=("Arial", 18, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 10))

        subtitle_label = ttk.Label(main_frame, text="Camp Management System",
                                  font=("Arial", 12))
        subtitle_label.grid(row=1, column=0, columnspan=2, pady=(0, 30))

        # اسم المستخدم
        ttk.Label(main_frame, text="اسم المستخدم:",
                 font=("Arial", 10, "bold")).grid(row=2, column=0, sticky=tk.W, pady=(0, 5))

        self.username_var = tk.StringVar()
        self.username_entry = ttk.Entry(main_frame, textvariable=self.username_var,
                                       font=("Arial", 10), width=25)
        self.username_entry.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))

        # كلمة المرور
        ttk.Label(main_frame, text="كلمة المرور:",
                 font=("Arial", 10, "bold")).grid(row=4, column=0, sticky=tk.W, pady=(0, 5))

        self.password_var = tk.StringVar()
        self.password_entry = ttk.Entry(main_frame, textvariable=self.password_var,
                                       show="*", font=("Arial", 10), width=25)
        self.password_entry.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))

        # أزرار
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=6, column=0, columnspan=2, pady=(10, 0))

        self.login_button = ttk.Button(buttons_frame, text="تسجيل الدخول",
                                      command=self.handle_login, width=15)
        self.login_button.pack(side=tk.LEFT, padx=(0, 10))

        self.cancel_button = ttk.Button(buttons_frame, text="إلغاء",
                                       command=self.root.quit, width=15)
        self.cancel_button.pack(side=tk.LEFT)

        # معلومات النظام
        info_frame = ttk.LabelFrame(main_frame, text="معلومات النظام", padding="10")
        info_frame.grid(row=7, column=0, columnspan=2, pady=(30, 0), sticky=(tk.W, tk.E))

        info_text = "نظام إدارة المخيم\nالإصدار 1.0.0"
        ttk.Label(info_frame, text=info_text, font=("Arial", 9),
                 justify=tk.CENTER).pack()

        # ربط Enter بتسجيل الدخول
        self.root.bind('<Return>', lambda event: self.handle_login())

        # تركيز على حقل اسم المستخدم
        self.username_entry.focus()

        # إنشاء المدير الافتراضي
        self.auth_service.create_default_admin()

    def handle_login(self):
        """معالجة تسجيل الدخول"""
        username = self.username_var.get().strip()
        password = self.password_var.get()

        # التحقق من الحقول
        if not username:
            messagebox.showwarning("خطأ", "يرجى إدخال اسم المستخدم")
            self.username_entry.focus()
            return

        if not password:
            messagebox.showwarning("خطأ", "يرجى إدخال كلمة المرور")
            self.password_entry.focus()
            return

        # تعطيل الأزرار أثناء المعالجة
        self.login_button.config(state='disabled', text="جاري التحقق...")
        self.root.update()

        try:
            # محاولة تسجيل الدخول
            success, message, user = self.auth_service.login(username, password)

            if success:
                self.current_user = user
                messagebox.showinfo("نجح تسجيل الدخول", message)
                self.root.destroy()
                self.open_main_window()
                return  # الخروج من الدالة بعد تدمير النافذة
            else:
                messagebox.showerror("فشل تسجيل الدخول", message)
                self.password_var.set("")
                self.password_entry.focus()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ غير متوقع: {str(e)}")

        finally:
            # إعادة تفعيل الأزرار فقط إذا كانت النافذة ما زالت موجودة
            try:
                if self.root.winfo_exists():
                    self.login_button.config(state='normal', text="تسجيل الدخول")
            except tk.TclError:
                pass  # النافذة تم تدميرها بالفعل

    def open_main_window(self):
        """فتح النافذة الرئيسية"""
        from .main_window import MainWindow
        main_app = MainWindow(self.current_user)
        main_app.run()

    def run(self):
        """تشغيل النافذة"""
        self.root.mainloop()

if __name__ == "__main__":
    app = LoginWindow()
    app.run()
