"""
النافذة الرئيسية باستخدام Tkinter
Main Window using Tkinter
"""
import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os
from datetime import datetime

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.services.auth_service import AuthService
from src.services.family_service import FamilyService
from src.services.excel_service import ExcelService

class MainWindow:
    """النافذة الرئيسية"""

    def __init__(self, user):
        self.current_user = user
        self.auth_service = AuthService()
        self.auth_service.current_user = user
        self.family_service = FamilyService()
        self.excel_service = ExcelService()

        self.root = tk.Tk()
        self.setup_window()
        self.create_widgets()
        self.load_data()

    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title(f"نظام إدارة المخيم - مرحباً {self.current_user.full_name}")
        self.root.geometry("1200x800")
        self.root.minsize(800, 600)

        # توسيط النافذة
        self.center_window()

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.root.update_idletasks()
        width = 1200
        height = 800
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # شريط القوائم
        self.create_menu()

        # شريط المعلومات العلوي
        self.create_info_bar()

        # التبويبات الرئيسية
        self.create_notebook()

        # شريط الحالة
        self.create_status_bar()

    def create_menu(self):
        """إنشاء شريط القوائم"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # قائمة الملف
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)

        if self.auth_service.has_permission("families", "create"):
            file_menu.add_command(label="أسرة جديدة", command=self.add_new_family)
            file_menu.add_separator()

        if self.auth_service.has_permission("reports", "export"):
            file_menu.add_command(label="تصدير البيانات", command=self.export_data)
            file_menu.add_separator()

        if self.auth_service.can_manage_users():
            file_menu.add_command(label="إدارة المستخدمين", command=self.open_user_management)
            file_menu.add_separator()

        file_menu.add_command(label="تسجيل الخروج", command=self.logout)
        file_menu.add_command(label="خروج", command=self.root.quit)

        # قائمة المساعدة
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="حول البرنامج", command=self.show_about)

    def create_info_bar(self):
        """إنشاء شريط المعلومات العلوي"""
        info_frame = ttk.Frame(self.root)
        info_frame.pack(fill=tk.X, padx=5, pady=5)

        # معلومات المستخدم
        user_info = f"المستخدم: {self.current_user.full_name} | الدور: {self.current_user.get_role_display()}"
        ttk.Label(info_frame, text=user_info, font=("Arial", 10, "bold")).pack(side=tk.LEFT)

        # التاريخ والوقت
        self.datetime_label = ttk.Label(info_frame, font=("Arial", 10))
        self.datetime_label.pack(side=tk.RIGHT)
        self.update_datetime()

    def create_notebook(self):
        """إنشاء التبويبات الرئيسية"""
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # تبويب لوحة التحكم
        self.create_dashboard_tab()

        # تبويب إدارة الأسر
        if self.auth_service.has_any_permission("families"):
            self.create_families_tab()

        # تبويب التقارير
        if self.auth_service.has_any_permission("reports"):
            self.create_reports_tab()

        # تبويب إدارة المستخدمين
        if self.auth_service.can_manage_users():
            self.create_users_tab()

        # تبويب توزيع المساعدات
        if self.auth_service.has_any_permission("aid_distribution"):
            self.create_aid_distribution_tab()

    def create_dashboard_tab(self):
        """إنشاء تبويب لوحة التحكم"""
        dashboard_frame = ttk.Frame(self.notebook)
        self.notebook.add(dashboard_frame, text="لوحة التحكم")

        # عنوان
        title_label = ttk.Label(dashboard_frame, text="لوحة التحكم الرئيسية",
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=20)

        # إطار الإحصائيات
        stats_frame = ttk.LabelFrame(dashboard_frame, text="الإحصائيات السريعة", padding="10")
        stats_frame.pack(fill=tk.X, padx=20, pady=10)

        # شبكة الإحصائيات
        stats_grid = ttk.Frame(stats_frame)
        stats_grid.pack(fill=tk.X)

        # بطاقات الإحصائيات
        self.stats_labels = {}

        # الصف الأول
        self.stats_labels['families'] = self.create_stat_card(stats_grid, "إجمالي الأسر", "0", 0, 0)
        self.stats_labels['individuals'] = self.create_stat_card(stats_grid, "إجمالي الأفراد", "0", 0, 1)
        self.stats_labels['children'] = self.create_stat_card(stats_grid, "الأطفال", "0", 0, 2)
        self.stats_labels['adults'] = self.create_stat_card(stats_grid, "البالغون", "0", 0, 3)

        # أزرار سريعة
        buttons_frame = ttk.Frame(dashboard_frame)
        buttons_frame.pack(pady=20)

        if self.auth_service.has_permission("families", "create"):
            ttk.Button(buttons_frame, text="إضافة أسرة جديدة",
                      command=self.add_new_family, width=20).pack(side=tk.LEFT, padx=10)

        if self.auth_service.has_permission("reports", "export"):
            ttk.Button(buttons_frame, text="تصدير التقارير",
                      command=self.export_data, width=20).pack(side=tk.LEFT, padx=10)

        ttk.Button(buttons_frame, text="تحديث البيانات",
                  command=self.load_data, width=20).pack(side=tk.LEFT, padx=10)

    def create_stat_card(self, parent, title, value, row, col):
        """إنشاء بطاقة إحصائية"""
        card_frame = ttk.LabelFrame(parent, text=title, padding="10")
        card_frame.grid(row=row, column=col, padx=10, pady=5, sticky=(tk.W, tk.E))

        value_label = ttk.Label(card_frame, text=value, font=("Arial", 20, "bold"))
        value_label.pack()

        # تكوين الشبكة
        parent.columnconfigure(col, weight=1)

        return value_label

    def create_families_tab(self):
        """إنشاء تبويب إدارة الأسر"""
        families_frame = ttk.Frame(self.notebook)
        self.notebook.add(families_frame, text="إدارة الأسر")

        # شريط الأدوات
        toolbar_frame = ttk.Frame(families_frame)
        toolbar_frame.pack(fill=tk.X, padx=5, pady=5)

        # البحث
        ttk.Label(toolbar_frame, text="البحث:").pack(side=tk.LEFT, padx=(0, 5))

        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(toolbar_frame, textvariable=self.search_var, width=30)
        search_entry.pack(side=tk.LEFT, padx=(0, 10))
        search_entry.bind('<KeyRelease>', self.search_families)

        # أزرار
        if self.auth_service.has_permission("families", "create"):
            ttk.Button(toolbar_frame, text="إضافة أسرة",
                      command=self.add_new_family).pack(side=tk.LEFT, padx=5)

        if self.auth_service.has_permission("families", "update"):
            ttk.Button(toolbar_frame, text="تعديل",
                      command=self.edit_family).pack(side=tk.LEFT, padx=5)

        ttk.Button(toolbar_frame, text="تحديث",
                  command=self.load_families).pack(side=tk.LEFT, padx=5)

        ttk.Button(toolbar_frame, text="إدارة الأعمدة",
                  command=self.open_table_manager).pack(side=tk.LEFT, padx=5)

        # جدول الأسر
        self.create_families_table(families_frame)

    def create_families_table(self, parent):
        """إنشاء جدول الأسر"""
        # إطار الجدول
        table_frame = ttk.Frame(parent)
        table_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # أعمدة الجدول
        columns = ("family_id", "head_name", "gender", "phone", "arrival_date", "tent_number", "members_count")

        self.families_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)

        # تعيين عناوين الأعمدة
        self.families_tree.heading("family_id", text="رقم الأسرة")
        self.families_tree.heading("head_name", text="اسم رب الأسرة")
        self.families_tree.heading("gender", text="الجنس")
        self.families_tree.heading("phone", text="رقم الهاتف")
        self.families_tree.heading("arrival_date", text="تاريخ الوصول")
        self.families_tree.heading("tent_number", text="رقم الخيمة")
        self.families_tree.heading("members_count", text="عدد الأفراد")

        # تعيين عرض الأعمدة
        self.families_tree.column("family_id", width=100)
        self.families_tree.column("head_name", width=200)
        self.families_tree.column("gender", width=80)
        self.families_tree.column("phone", width=120)
        self.families_tree.column("arrival_date", width=100)
        self.families_tree.column("tent_number", width=100)
        self.families_tree.column("members_count", width=100)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.families_tree.yview)
        self.families_tree.configure(yscrollcommand=scrollbar.set)

        # تخطيط الجدول
        self.families_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # ربط النقر المزدوج
        self.families_tree.bind("<Double-1>", self.view_family_details)

    def create_reports_tab(self):
        """إنشاء تبويب التقارير"""
        reports_frame = ttk.Frame(self.notebook)
        self.notebook.add(reports_frame, text="التقارير")

        # عنوان
        title_label = ttk.Label(reports_frame, text="التقارير والإحصائيات",
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=20)

        # أزرار التقارير
        buttons_frame = ttk.Frame(reports_frame)
        buttons_frame.pack(pady=20)

        ttk.Button(buttons_frame, text="تصدير جميع الأسر إلى Excel",
                  command=self.export_all_families, width=25).pack(pady=5)

        ttk.Button(buttons_frame, text="إنشاء قالب الأسر",
                  command=self.create_excel_template, width=25).pack(pady=5)

        ttk.Button(buttons_frame, text="إنشاء قالب الأفراد",
                  command=self.create_individuals_template, width=25).pack(pady=5)

        ttk.Button(buttons_frame, text="استيراد من Excel",
                  command=self.open_import_excel, width=25).pack(pady=5)

        ttk.Button(buttons_frame, text="تقرير الإحصائيات",
                  command=self.show_statistics_report, width=25).pack(pady=5)

    def create_aid_distribution_tab(self):
        """إنشاء تبويب توزيع المساعدات"""
        aid_frame = ttk.Frame(self.notebook)
        self.notebook.add(aid_frame, text="توزيع المساعدات")

        # عنوان
        title_label = ttk.Label(aid_frame, text="إدارة توزيع المساعدات",
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=20)

        # أزرار سريعة
        buttons_frame = ttk.Frame(aid_frame)
        buttons_frame.pack(pady=20)

        ttk.Button(buttons_frame, text="فتح نافذة توزيع المساعدات",
                  command=self.open_aid_distribution_window, width=30).pack(pady=10)

        # معلومات سريعة
        info_frame = ttk.LabelFrame(aid_frame, text="معلومات سريعة", padding="20")
        info_frame.pack(fill=tk.X, padx=20, pady=10)

        info_text = """وحدة توزيع المساعدات تتيح لك:

• تسجيل عمليات توزيع المساعدات للأسر
• متابعة أنواع المساعدات المختلفة (غذائية، طبية، ملابس، إلخ)
• إنشاء تقارير توزيع شاملة
• متابعة الأسر المستفيدة وغير المستفيدة
• تصدير بيانات التوزيع إلى Excel

انقر على الزر أعلاه لفتح نافذة إدارة توزيع المساعدات الكاملة."""

        ttk.Label(info_frame, text=info_text, font=("Arial", 11),
                 justify=tk.LEFT).pack(anchor=tk.W)

    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_bar = ttk.Label(self.root, text="جاهز", relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)

    def update_datetime(self):
        """تحديث التاريخ والوقت"""
        now = datetime.now()
        self.datetime_label.config(text=now.strftime("%Y-%m-%d %H:%M:%S"))
        self.root.after(1000, self.update_datetime)

    def load_data(self):
        """تحميل البيانات"""
        try:
            self.status_bar.config(text="جاري تحميل البيانات...")
            self.root.update()

            # تحميل الإحصائيات
            stats = self.family_service.get_statistics()

            self.stats_labels['families'].config(text=str(stats.get('total_families', 0)))
            self.stats_labels['individuals'].config(text=str(stats.get('total_individuals', 0)))
            self.stats_labels['children'].config(text=str(stats.get('children_count', 0)))
            self.stats_labels['adults'].config(text=str(stats.get('adults_count', 0)))

            # تحميل الأسر
            self.load_families()

            self.status_bar.config(text="تم تحديث البيانات بنجاح")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل البيانات: {str(e)}")
            self.status_bar.config(text="فشل في تحميل البيانات")

    def load_families(self):
        """تحميل بيانات الأسر"""
        try:
            # مسح البيانات الحالية
            for item in self.families_tree.get_children():
                self.families_tree.delete(item)

            # تحميل الأسر
            families = self.family_service.get_all_families()

            for family in families:
                arrival_date = family.arrival_date.strftime('%Y-%m-%d') if family.arrival_date else ""

                self.families_tree.insert("", tk.END, values=(
                    family.family_id,
                    family.head_of_family_name,
                    family.head_gender or "",
                    family.phone_number or "",
                    arrival_date,
                    family.tent_number or "",
                    family.get_total_members()
                ))

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل الأسر: {str(e)}")

    def search_families(self, event=None):
        """البحث في الأسر"""
        search_term = self.search_var.get().strip()

        # مسح البيانات الحالية
        for item in self.families_tree.get_children():
            self.families_tree.delete(item)

        try:
            if search_term:
                families = self.family_service.search_families(search_term)
            else:
                families = self.family_service.get_all_families()

            for family in families:
                arrival_date = family.arrival_date.strftime('%Y-%m-%d') if family.arrival_date else ""

                self.families_tree.insert("", tk.END, values=(
                    family.family_id,
                    family.head_of_family_name,
                    family.head_gender or "",
                    family.phone_number or "",
                    arrival_date,
                    family.tent_number or "",
                    family.get_total_members()
                ))

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في البحث: {str(e)}")

    # الدوال الأخرى
    def add_new_family(self):
        """إضافة أسرة جديدة"""
        try:
            from .family_form_window import FamilyFormWindow
            FamilyFormWindow(self.root, self.current_user)
            # تحديث البيانات بعد إغلاق النافذة
            self.load_data()
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح نافذة إضافة الأسرة: {str(e)}")

    def edit_family(self):
        """تعديل أسرة"""
        selection = self.families_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار أسرة للتعديل")
            return

        try:
            # الحصول على رقم الأسرة من الصف المحدد
            item = self.families_tree.item(selection[0])
            family_id = item['values'][0]  # رقم الأسرة في العمود الأول

            from .family_form_window import FamilyFormWindow
            FamilyFormWindow(self.root, self.current_user, family_id)
            # تحديث البيانات بعد إغلاق النافذة
            self.load_data()
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح نافذة تعديل الأسرة: {str(e)}")

    def view_family_details(self, event):
        """عرض تفاصيل الأسرة"""
        selection = self.families_tree.selection()
        if not selection:
            return

        try:
            # الحصول على رقم الأسرة من الصف المحدد
            item = self.families_tree.item(selection[0])
            family_id = item['values'][0]  # رقم الأسرة في العمود الأول

            # فتح نافذة التفاصيل
            self.show_family_details_window(family_id)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في عرض تفاصيل الأسرة: {str(e)}")

    def show_family_details_window(self, family_id):
        """عرض نافذة تفاصيل الأسرة"""
        try:
            family = self.family_service.get_family_by_id(family_id)
            if not family:
                messagebox.showerror("خطأ", "لم يتم العثور على الأسرة")
                return

            # إنشاء نافذة التفاصيل
            details_window = tk.Toplevel(self.root)
            details_window.title(f"تفاصيل الأسرة - {family.family_id}")
            details_window.geometry("600x500")
            details_window.resizable(True, True)

            # توسيط النافذة
            details_window.update_idletasks()
            width = 600
            height = 500
            x = (details_window.winfo_screenwidth() // 2) - (width // 2)
            y = (details_window.winfo_screenheight() // 2) - (height // 2)
            details_window.geometry(f"{width}x{height}+{x}+{y}")

            # جعل النافذة modal
            details_window.transient(self.root)
            details_window.grab_set()

            # إنشاء محتوى النافذة
            main_frame = ttk.Frame(details_window, padding="20")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # العنوان
            title_label = ttk.Label(main_frame, text=f"تفاصيل الأسرة: {family.family_id}",
                                   font=("Arial", 16, "bold"))
            title_label.pack(pady=(0, 20))

            # إنشاء تبويبات
            notebook = ttk.Notebook(main_frame)
            notebook.pack(fill=tk.BOTH, expand=True)

            # تبويب بيانات الأسرة
            family_frame = ttk.Frame(notebook)
            notebook.add(family_frame, text="بيانات الأسرة")

            family_info = f"""رقم الأسرة: {family.family_id}
اسم رب الأسرة: {family.head_of_family_name}
تاريخ الميلاد: {family.head_birth_date.strftime('%Y-%m-%d') if family.head_birth_date else 'غير محدد'}
الجنس: {family.head_gender or 'غير محدد'}
الحالة الاجتماعية: {family.head_marital_status or 'غير محدد'}
رقم الهاتف: {family.phone_number or 'غير محدد'}
رقم الخيمة: {family.tent_number or 'غير محدد'}
تاريخ الوصول: {family.arrival_date.strftime('%Y-%m-%d') if family.arrival_date else 'غير محدد'}
الجنسية: {family.nationality or 'غير محدد'}
بلد المنشأ: {family.origin_country or 'غير محدد'}
مدينة المنشأ: {family.origin_city or 'غير محدد'}
عدد أفراد الأسرة: {family.get_total_members()}
ملاحظات: {family.notes or 'لا توجد ملاحظات'}"""

            family_text = tk.Text(family_frame, wrap=tk.WORD, font=("Arial", 10))
            family_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            family_text.insert(1.0, family_info)
            family_text.config(state="disabled")

            # تبويب أفراد الأسرة
            individuals_frame = ttk.Frame(notebook)
            notebook.add(individuals_frame, text="أفراد الأسرة")

            if family.individuals:
                # جدول الأفراد
                columns = ("name", "birth_date", "gender", "relationship")
                individuals_tree = ttk.Treeview(individuals_frame, columns=columns, show="headings")

                individuals_tree.heading("name", text="الاسم")
                individuals_tree.heading("birth_date", text="تاريخ الميلاد")
                individuals_tree.heading("gender", text="الجنس")
                individuals_tree.heading("relationship", text="صلة القرابة")

                for individual in family.individuals:
                    birth_date = individual.birth_date.strftime('%Y-%m-%d') if individual.birth_date else ""
                    individuals_tree.insert("", tk.END, values=(
                        individual.full_name,
                        birth_date,
                        individual.gender or "",
                        individual.relationship_to_head or ""
                    ))

                individuals_tree.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            else:
                ttk.Label(individuals_frame, text="لا يوجد أفراد مسجلون لهذه الأسرة",
                         font=("Arial", 12)).pack(expand=True)

            # أزرار
            buttons_frame = ttk.Frame(main_frame)
            buttons_frame.pack(fill=tk.X, pady=(20, 0))

            ttk.Button(buttons_frame, text="تعديل",
                      command=lambda: self.edit_family_from_details(family_id, details_window)).pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(buttons_frame, text="إغلاق",
                      command=details_window.destroy).pack(side=tk.RIGHT)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في عرض تفاصيل الأسرة: {str(e)}")

    def edit_family_from_details(self, family_id, details_window):
        """تعديل الأسرة من نافذة التفاصيل"""
        details_window.destroy()
        try:
            from .family_form_window import FamilyFormWindow
            FamilyFormWindow(self.root, self.current_user, family_id)
            self.load_data()
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح نافذة التعديل: {str(e)}")

    def export_data(self):
        """تصدير البيانات"""
        self.export_all_families()

    def export_all_families(self):
        """تصدير جميع الأسر"""
        try:
            families = self.family_service.get_all_families()
            if not families:
                messagebox.showwarning("تحذير", "لا توجد بيانات للتصدير")
                return

            filename = f"families_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            success = self.excel_service.export_families_to_excel(families, filename)

            if success:
                messagebox.showinfo("نجح", f"تم تصدير البيانات إلى: {filename}")
            else:
                messagebox.showerror("خطأ", "فشل في تصدير البيانات")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في التصدير: {str(e)}")

    def create_excel_template(self):
        """إنشاء قالب Excel"""
        try:
            filename = f"families_template_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            success = self.excel_service.create_families_template(filename)

            if success:
                messagebox.showinfo("نجح", f"تم إنشاء القالب: {filename}")
            else:
                messagebox.showerror("خطأ", "فشل في إنشاء القالب")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء القالب: {str(e)}")

    def create_individuals_template(self):
        """إنشاء قالب الأفراد"""
        try:
            filename = f"individuals_template_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            success = self.excel_service.create_individuals_template(filename)

            if success:
                messagebox.showinfo("نجح", f"تم إنشاء قالب الأفراد: {filename}")
            else:
                messagebox.showerror("خطأ", "فشل في إنشاء القالب")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء قالب الأفراد: {str(e)}")

    def open_import_excel(self):
        """فتح نافذة استيراد Excel"""
        try:
            from .import_excel_window import ImportExcelWindow
            ImportExcelWindow(self.root, self.current_user)
            # تحديث البيانات بعد إغلاق النافذة
            self.load_data()
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح نافذة الاستيراد: {str(e)}")

    def show_statistics_report(self):
        """عرض تقرير الإحصائيات"""
        try:
            # إنشاء نافذة التقرير
            report_window = tk.Toplevel(self.root)
            report_window.title("تقرير الإحصائيات التفصيلي")
            report_window.geometry("800x600")
            report_window.resizable(True, True)

            # توسيط النافذة
            report_window.update_idletasks()
            width = 800
            height = 600
            x = (report_window.winfo_screenwidth() // 2) - (width // 2)
            y = (report_window.winfo_screenheight() // 2) - (height // 2)
            report_window.geometry(f"{width}x{height}+{x}+{y}")

            # جعل النافذة modal
            report_window.transient(self.root)
            report_window.grab_set()

            # إطار رئيسي
            main_frame = ttk.Frame(report_window, padding="20")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # العنوان
            title_label = ttk.Label(main_frame, text="📊 تقرير الإحصائيات التفصيلي",
                                   font=("Arial", 18, "bold"))
            title_label.pack(pady=(0, 20))

            # إنشاء تبويبات
            notebook = ttk.Notebook(main_frame)
            notebook.pack(fill=tk.BOTH, expand=True)

            # تبويب الإحصائيات العامة
            self.create_general_stats_tab(notebook)

            # تبويب إحصائيات الأسر
            self.create_family_stats_tab(notebook)

            # تبويب إحصائيات الأفراد
            self.create_individual_stats_tab(notebook)

            # أزرار
            buttons_frame = ttk.Frame(main_frame)
            buttons_frame.pack(fill=tk.X, pady=(20, 0))

            ttk.Button(buttons_frame, text="تصدير التقرير إلى PDF",
                      command=self.export_statistics_pdf).pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(buttons_frame, text="طباعة التقرير",
                      command=self.print_statistics).pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(buttons_frame, text="إغلاق",
                      command=report_window.destroy).pack(side=tk.RIGHT)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء التقرير: {str(e)}")

    def create_general_stats_tab(self, notebook):
        """إنشاء تبويب الإحصائيات العامة"""
        general_frame = ttk.Frame(notebook)
        notebook.add(general_frame, text="الإحصائيات العامة")

        try:
            stats = self.family_service.get_statistics()

            # إطار الإحصائيات الرئيسية
            main_stats_frame = ttk.LabelFrame(general_frame, text="الإحصائيات الرئيسية", padding="15")
            main_stats_frame.pack(fill=tk.X, padx=10, pady=10)

            # شبكة الإحصائيات
            stats_grid = ttk.Frame(main_stats_frame)
            stats_grid.pack(fill=tk.X)

            # بطاقات الإحصائيات
            self.create_stat_card_detailed(stats_grid, "👥 إجمالي الأسر", str(stats['total_families']),
                                         "أسرة مسجلة في النظام", 0, 0)
            self.create_stat_card_detailed(stats_grid, "👤 إجمالي الأفراد", str(stats['total_individuals']),
                                         "فرد في جميع الأسر", 0, 1)
            self.create_stat_card_detailed(stats_grid, "🧒 الأطفال", str(stats['children_count']),
                                         "طفل أقل من 18 سنة", 1, 0)
            self.create_stat_card_detailed(stats_grid, "👨 البالغون", str(stats['adults_count']),
                                         "بالغ 18 سنة فأكثر", 1, 1)

            # معلومات إضافية
            additional_frame = ttk.LabelFrame(general_frame, text="معلومات إضافية", padding="15")
            additional_frame.pack(fill=tk.X, padx=10, pady=10)

            additional_info = f"""📈 متوسط حجم الأسرة: {stats['average_family_size']} فرد/أسرة

📅 تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d')}
🕐 وقت التقرير: {datetime.now().strftime('%H:%M:%S')}

📊 نسبة الأطفال: {(stats['children_count'] / max(stats['total_individuals'], 1) * 100):.1f}%
📊 نسبة البالغين: {(stats['adults_count'] / max(stats['total_individuals'], 1) * 100):.1f}%"""

            ttk.Label(additional_frame, text=additional_info, font=("Arial", 11),
                     justify=tk.LEFT).pack(anchor=tk.W)

        except Exception as e:
            ttk.Label(general_frame, text=f"خطأ في تحميل الإحصائيات: {str(e)}",
                     font=("Arial", 12), foreground="red").pack(expand=True)

    def create_family_stats_tab(self, notebook):
        """إنشاء تبويب إحصائيات الأسر"""
        family_frame = ttk.Frame(notebook)
        notebook.add(family_frame, text="إحصائيات الأسر")

        try:
            families = self.family_service.get_all_families()

            # إحصائيات حسب الجنسية
            nationality_stats = {}
            gender_stats = {"ذكر": 0, "أنثى": 0, "غير محدد": 0}
            tent_count = 0

            for family in families:
                # إحصائيات الجنسية
                nationality = family.nationality or "غير محدد"
                nationality_stats[nationality] = nationality_stats.get(nationality, 0) + 1

                # إحصائيات الجنس
                gender = family.head_gender or "غير محدد"
                gender_stats[gender] = gender_stats.get(gender, 0) + 1

                # عدد الخيام
                if family.tent_number:
                    tent_count += 1

            # عرض الإحصائيات
            nationality_frame = ttk.LabelFrame(family_frame, text="التوزيع حسب الجنسية", padding="15")
            nationality_frame.pack(fill=tk.X, padx=10, pady=10)

            nationality_text = "الجنسيات المسجلة:\n\n"
            for nationality, count in sorted(nationality_stats.items(), key=lambda x: x[1], reverse=True):
                percentage = (count / len(families) * 100) if families else 0
                nationality_text += f"🌍 {nationality}: {count} أسرة ({percentage:.1f}%)\n"

            ttk.Label(nationality_frame, text=nationality_text, font=("Arial", 11),
                     justify=tk.LEFT).pack(anchor=tk.W)

            # إحصائيات أخرى
            other_frame = ttk.LabelFrame(family_frame, text="إحصائيات أخرى", padding="15")
            other_frame.pack(fill=tk.X, padx=10, pady=10)

            other_text = f"""👨‍👩‍👧‍👦 توزيع رؤساء الأسر حسب الجنس:
• ذكر: {gender_stats['ذكر']} أسرة
• أنثى: {gender_stats['أنثى']} أسرة
• غير محدد: {gender_stats['غير محدد']} أسرة

🏠 الخيام والمساكن:
• أسر لديها رقم خيمة: {tent_count} أسرة
• أسر بدون رقم خيمة: {len(families) - tent_count} أسرة"""

            ttk.Label(other_frame, text=other_text, font=("Arial", 11),
                     justify=tk.LEFT).pack(anchor=tk.W)

        except Exception as e:
            ttk.Label(family_frame, text=f"خطأ في تحميل إحصائيات الأسر: {str(e)}",
                     font=("Arial", 12), foreground="red").pack(expand=True)

    def create_individual_stats_tab(self, notebook):
        """إنشاء تبويب إحصائيات الأفراد"""
        individual_frame = ttk.Frame(notebook)
        notebook.add(individual_frame, text="إحصائيات الأفراد")

        try:
            families = self.family_service.get_all_families()

            # جمع بيانات الأفراد
            total_individuals = 0
            age_groups = {"0-5": 0, "6-12": 0, "13-17": 0, "18-30": 0, "31-50": 0, "50+": 0, "غير محدد": 0}
            gender_distribution = {"ذكر": 0, "أنثى": 0, "غير محدد": 0}

            for family in families:
                # رب الأسرة
                total_individuals += 1
                if family.head_gender:
                    gender_distribution[family.head_gender] += 1
                else:
                    gender_distribution["غير محدد"] += 1

                # حساب عمر رب الأسرة
                if family.head_birth_date:
                    age = (datetime.now().date() - family.head_birth_date).days // 365
                    if age <= 5:
                        age_groups["0-5"] += 1
                    elif age <= 12:
                        age_groups["6-12"] += 1
                    elif age <= 17:
                        age_groups["13-17"] += 1
                    elif age <= 30:
                        age_groups["18-30"] += 1
                    elif age <= 50:
                        age_groups["31-50"] += 1
                    else:
                        age_groups["50+"] += 1
                else:
                    age_groups["غير محدد"] += 1

                # أفراد الأسرة
                for individual in family.individuals:
                    total_individuals += 1

                    # الجنس
                    if individual.gender:
                        gender_distribution[individual.gender] += 1
                    else:
                        gender_distribution["غير محدد"] += 1

                    # العمر
                    if individual.birth_date:
                        age = (datetime.now().date() - individual.birth_date).days // 365
                        if age <= 5:
                            age_groups["0-5"] += 1
                        elif age <= 12:
                            age_groups["6-12"] += 1
                        elif age <= 17:
                            age_groups["13-17"] += 1
                        elif age <= 30:
                            age_groups["18-30"] += 1
                        elif age <= 50:
                            age_groups["31-50"] += 1
                        else:
                            age_groups["50+"] += 1
                    else:
                        age_groups["غير محدد"] += 1

            # عرض التوزيع العمري
            age_frame = ttk.LabelFrame(individual_frame, text="التوزيع العمري", padding="15")
            age_frame.pack(fill=tk.X, padx=10, pady=10)

            age_text = "الفئات العمرية:\n\n"
            for age_group, count in age_groups.items():
                percentage = (count / total_individuals * 100) if total_individuals else 0
                age_text += f"👶 {age_group} سنة: {count} فرد ({percentage:.1f}%)\n"

            ttk.Label(age_frame, text=age_text, font=("Arial", 11),
                     justify=tk.LEFT).pack(anchor=tk.W)

            # عرض التوزيع حسب الجنس
            gender_frame = ttk.LabelFrame(individual_frame, text="التوزيع حسب الجنس", padding="15")
            gender_frame.pack(fill=tk.X, padx=10, pady=10)

            gender_text = "توزيع الأفراد حسب الجنس:\n\n"
            for gender, count in gender_distribution.items():
                percentage = (count / total_individuals * 100) if total_individuals else 0
                icon = "👨" if gender == "ذكر" else "👩" if gender == "أنثى" else "❓"
                gender_text += f"{icon} {gender}: {count} فرد ({percentage:.1f}%)\n"

            ttk.Label(gender_frame, text=gender_text, font=("Arial", 11),
                     justify=tk.LEFT).pack(anchor=tk.W)

        except Exception as e:
            ttk.Label(individual_frame, text=f"خطأ في تحميل إحصائيات الأفراد: {str(e)}",
                     font=("Arial", 12), foreground="red").pack(expand=True)

    def create_stat_card_detailed(self, parent, title, value, description, row, col):
        """إنشاء بطاقة إحصائية مفصلة"""
        card_frame = ttk.LabelFrame(parent, text=title, padding="15")
        card_frame.grid(row=row, column=col, padx=10, pady=10, sticky=(tk.W, tk.E, tk.N, tk.S))

        # القيمة الرئيسية
        value_label = ttk.Label(card_frame, text=value, font=("Arial", 24, "bold"),
                               foreground="#2E86AB")
        value_label.pack()

        # الوصف
        desc_label = ttk.Label(card_frame, text=description, font=("Arial", 10),
                              foreground="#666666")
        desc_label.pack()

        # تكوين الشبكة
        parent.columnconfigure(col, weight=1)
        parent.rowconfigure(row, weight=1)

    def export_statistics_pdf(self):
        """تصدير التقرير إلى PDF"""
        messagebox.showinfo("قيد التطوير", "ميزة تصدير PDF قيد التطوير")

    def print_statistics(self):
        """طباعة التقرير"""
        messagebox.showinfo("قيد التطوير", "ميزة الطباعة قيد التطوير")

    def logout(self):
        """تسجيل الخروج"""
        if messagebox.askyesno("تسجيل الخروج", "هل أنت متأكد من تسجيل الخروج؟"):
            self.root.destroy()
            from .login_window import LoginWindow
            login_app = LoginWindow()
            login_app.run()

    def show_about(self):
        """عرض معلومات حول البرنامج"""
        about_text = """نظام إدارة المخيم
الإصدار 1.0.0

نظام شامل لإدارة مخيمات اللاجئين
يوفر أدوات قوية لإدارة البيانات والتقارير

تم التطوير باستخدام Python و Tkinter"""

        messagebox.showinfo("حول البرنامج", about_text)

    def open_user_management(self):
        """فتح نافذة إدارة المستخدمين"""
        try:
            from .user_management_window import UserManagementWindow
            UserManagementWindow(self.root, self.current_user)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح نافذة إدارة المستخدمين: {str(e)}")

    def open_table_manager(self):
        """فتح نافذة إدارة الجداول والأعمدة"""
        try:
            from .table_manager_window import TableManagerWindow
            TableManagerWindow(self.root, self.current_user)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح نافذة إدارة الجداول: {str(e)}")

    def open_aid_distribution_window(self):
        """فتح نافذة توزيع المساعدات"""
        try:
            from .aid_distribution_window import AidDistributionWindow
            AidDistributionWindow(self.root, self.current_user)
            # تحديث البيانات بعد إغلاق النافذة
            self.load_data()
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح نافذة توزيع المساعدات: {str(e)}")

    def create_users_tab(self):
        """إنشاء تبويب إدارة المستخدمين"""
        users_frame = ttk.Frame(self.notebook)
        self.notebook.add(users_frame, text="إدارة المستخدمين")

        # عنوان
        title_label = ttk.Label(users_frame, text="إدارة المستخدمين",
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=20)

        # أزرار إدارة المستخدمين
        buttons_frame = ttk.Frame(users_frame)
        buttons_frame.pack(pady=20)

        ttk.Button(buttons_frame, text="فتح نافذة إدارة المستخدمين",
                  command=self.open_user_management, width=25).pack(pady=5)

        # معلومات سريعة
        info_frame = ttk.LabelFrame(users_frame, text="معلومات سريعة", padding="10")
        info_frame.pack(fill=tk.X, padx=20, pady=10)

        try:
            users = self.auth_service.get_all_users()
            user_count = len(users)
            admin_count = len([u for u in users if u.role == "admin"])

            info_text = f"إجمالي المستخدمين: {user_count}\nالمديرون: {admin_count}"
            ttk.Label(info_frame, text=info_text, font=("Arial", 12)).pack()
        except:
            ttk.Label(info_frame, text="فشل في تحميل معلومات المستخدمين").pack()

    def create_aid_distribution_tab(self):
        """إنشاء تبويب توزيع المساعدات"""
        aid_frame = ttk.Frame(self.notebook)
        self.notebook.add(aid_frame, text="توزيع المساعدات")

        # عنوان
        title_label = ttk.Label(aid_frame, text="إدارة توزيع المساعدات",
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=20)

        # رسالة قيد التطوير
        dev_frame = ttk.LabelFrame(aid_frame, text="حالة التطوير", padding="20")
        dev_frame.pack(fill=tk.X, padx=20, pady=20)

        dev_text = """هذه الوحدة قيد التطوير وستتضمن:

• تسجيل عمليات توزيع المساعدات
• ربط التوزيع بالأسر المستفيدة
• تقارير التوزيع والمتابعة
• إدارة أنواع المساعدات المختلفة"""

        ttk.Label(dev_frame, text=dev_text, font=("Arial", 10),
                 justify=tk.LEFT).pack()

    def run(self):
        """تشغيل النافذة"""
        self.root.mainloop()

if __name__ == "__main__":
    # للاختبار
    from src.services.auth_service import AuthService
    auth = AuthService()
    auth.create_default_admin()
    success, message, user = auth.login("admin", "admin123")
    if success:
        app = MainWindow(user)
        app.run()
