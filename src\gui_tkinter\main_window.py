"""
النافذة الرئيسية باستخدام Tkinter
Main Window using Tkinter
"""
import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os
from datetime import datetime

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.services.auth_service import AuthService
from src.services.family_service import FamilyService
from src.services.excel_service import ExcelService

class MainWindow:
    """النافذة الرئيسية"""
    
    def __init__(self, user):
        self.current_user = user
        self.auth_service = AuthService()
        self.auth_service.current_user = user
        self.family_service = FamilyService()
        self.excel_service = ExcelService()
        
        self.root = tk.Tk()
        self.setup_window()
        self.create_widgets()
        self.load_data()
        
    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title(f"نظام إدارة المخيم - مرحباً {self.current_user.full_name}")
        self.root.geometry("1200x800")
        self.root.minsize(800, 600)
        
        # توسيط النافذة
        self.center_window()
        
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.root.update_idletasks()
        width = 1200
        height = 800
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
        
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # شريط القوائم
        self.create_menu()
        
        # شريط المعلومات العلوي
        self.create_info_bar()
        
        # التبويبات الرئيسية
        self.create_notebook()
        
        # شريط الحالة
        self.create_status_bar()
        
    def create_menu(self):
        """إنشاء شريط القوائم"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # قائمة الملف
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        
        if self.auth_service.has_permission("create"):
            file_menu.add_command(label="أسرة جديدة", command=self.add_new_family)
            file_menu.add_separator()
        
        if self.auth_service.has_permission("reports"):
            file_menu.add_command(label="تصدير البيانات", command=self.export_data)
            file_menu.add_separator()
        
        file_menu.add_command(label="تسجيل الخروج", command=self.logout)
        file_menu.add_command(label="خروج", command=self.root.quit)
        
        # قائمة المساعدة
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="حول البرنامج", command=self.show_about)
        
    def create_info_bar(self):
        """إنشاء شريط المعلومات العلوي"""
        info_frame = ttk.Frame(self.root)
        info_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # معلومات المستخدم
        user_info = f"المستخدم: {self.current_user.full_name} | الدور: {self.current_user.get_role_display()}"
        ttk.Label(info_frame, text=user_info, font=("Arial", 10, "bold")).pack(side=tk.LEFT)
        
        # التاريخ والوقت
        self.datetime_label = ttk.Label(info_frame, font=("Arial", 10))
        self.datetime_label.pack(side=tk.RIGHT)
        self.update_datetime()
        
    def create_notebook(self):
        """إنشاء التبويبات الرئيسية"""
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # تبويب لوحة التحكم
        self.create_dashboard_tab()
        
        # تبويب إدارة الأسر
        if self.auth_service.has_permission("read"):
            self.create_families_tab()
        
        # تبويب التقارير
        if self.auth_service.has_permission("reports"):
            self.create_reports_tab()
            
    def create_dashboard_tab(self):
        """إنشاء تبويب لوحة التحكم"""
        dashboard_frame = ttk.Frame(self.notebook)
        self.notebook.add(dashboard_frame, text="لوحة التحكم")
        
        # عنوان
        title_label = ttk.Label(dashboard_frame, text="لوحة التحكم الرئيسية", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=20)
        
        # إطار الإحصائيات
        stats_frame = ttk.LabelFrame(dashboard_frame, text="الإحصائيات السريعة", padding="10")
        stats_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # شبكة الإحصائيات
        stats_grid = ttk.Frame(stats_frame)
        stats_grid.pack(fill=tk.X)
        
        # بطاقات الإحصائيات
        self.stats_labels = {}
        
        # الصف الأول
        self.stats_labels['families'] = self.create_stat_card(stats_grid, "إجمالي الأسر", "0", 0, 0)
        self.stats_labels['individuals'] = self.create_stat_card(stats_grid, "إجمالي الأفراد", "0", 0, 1)
        self.stats_labels['children'] = self.create_stat_card(stats_grid, "الأطفال", "0", 0, 2)
        self.stats_labels['adults'] = self.create_stat_card(stats_grid, "البالغون", "0", 0, 3)
        
        # أزرار سريعة
        if self.auth_service.has_permission("create"):
            buttons_frame = ttk.Frame(dashboard_frame)
            buttons_frame.pack(pady=20)
            
            ttk.Button(buttons_frame, text="إضافة أسرة جديدة", 
                      command=self.add_new_family, width=20).pack(side=tk.LEFT, padx=10)
            
        if self.auth_service.has_permission("reports"):
            ttk.Button(buttons_frame, text="تصدير التقارير", 
                      command=self.export_data, width=20).pack(side=tk.LEFT, padx=10)
            
        ttk.Button(buttons_frame, text="تحديث البيانات", 
                  command=self.load_data, width=20).pack(side=tk.LEFT, padx=10)
        
    def create_stat_card(self, parent, title, value, row, col):
        """إنشاء بطاقة إحصائية"""
        card_frame = ttk.LabelFrame(parent, text=title, padding="10")
        card_frame.grid(row=row, column=col, padx=10, pady=5, sticky=(tk.W, tk.E))
        
        value_label = ttk.Label(card_frame, text=value, font=("Arial", 20, "bold"))
        value_label.pack()
        
        # تكوين الشبكة
        parent.columnconfigure(col, weight=1)
        
        return value_label
        
    def create_families_tab(self):
        """إنشاء تبويب إدارة الأسر"""
        families_frame = ttk.Frame(self.notebook)
        self.notebook.add(families_frame, text="إدارة الأسر")
        
        # شريط الأدوات
        toolbar_frame = ttk.Frame(families_frame)
        toolbar_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # البحث
        ttk.Label(toolbar_frame, text="البحث:").pack(side=tk.LEFT, padx=(0, 5))
        
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(toolbar_frame, textvariable=self.search_var, width=30)
        search_entry.pack(side=tk.LEFT, padx=(0, 10))
        search_entry.bind('<KeyRelease>', self.search_families)
        
        # أزرار
        if self.auth_service.has_permission("create"):
            ttk.Button(toolbar_frame, text="إضافة أسرة", 
                      command=self.add_new_family).pack(side=tk.LEFT, padx=5)
        
        if self.auth_service.has_permission("update"):
            ttk.Button(toolbar_frame, text="تعديل", 
                      command=self.edit_family).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(toolbar_frame, text="تحديث", 
                  command=self.load_families).pack(side=tk.LEFT, padx=5)
        
        # جدول الأسر
        self.create_families_table(families_frame)
        
    def create_families_table(self, parent):
        """إنشاء جدول الأسر"""
        # إطار الجدول
        table_frame = ttk.Frame(parent)
        table_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # أعمدة الجدول
        columns = ("family_id", "head_name", "gender", "phone", "arrival_date", "tent_number", "members_count")
        
        self.families_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)
        
        # تعيين عناوين الأعمدة
        self.families_tree.heading("family_id", text="رقم الأسرة")
        self.families_tree.heading("head_name", text="اسم رب الأسرة")
        self.families_tree.heading("gender", text="الجنس")
        self.families_tree.heading("phone", text="رقم الهاتف")
        self.families_tree.heading("arrival_date", text="تاريخ الوصول")
        self.families_tree.heading("tent_number", text="رقم الخيمة")
        self.families_tree.heading("members_count", text="عدد الأفراد")
        
        # تعيين عرض الأعمدة
        self.families_tree.column("family_id", width=100)
        self.families_tree.column("head_name", width=200)
        self.families_tree.column("gender", width=80)
        self.families_tree.column("phone", width=120)
        self.families_tree.column("arrival_date", width=100)
        self.families_tree.column("tent_number", width=100)
        self.families_tree.column("members_count", width=100)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.families_tree.yview)
        self.families_tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.families_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط النقر المزدوج
        self.families_tree.bind("<Double-1>", self.view_family_details)
        
    def create_reports_tab(self):
        """إنشاء تبويب التقارير"""
        reports_frame = ttk.Frame(self.notebook)
        self.notebook.add(reports_frame, text="التقارير")
        
        # عنوان
        title_label = ttk.Label(reports_frame, text="التقارير والإحصائيات", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=20)
        
        # أزرار التقارير
        buttons_frame = ttk.Frame(reports_frame)
        buttons_frame.pack(pady=20)
        
        ttk.Button(buttons_frame, text="تصدير جميع الأسر إلى Excel", 
                  command=self.export_all_families, width=25).pack(pady=5)
        
        ttk.Button(buttons_frame, text="إنشاء قالب Excel", 
                  command=self.create_excel_template, width=25).pack(pady=5)
        
        ttk.Button(buttons_frame, text="تقرير الإحصائيات", 
                  command=self.show_statistics_report, width=25).pack(pady=5)
        
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_bar = ttk.Label(self.root, text="جاهز", relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
    def update_datetime(self):
        """تحديث التاريخ والوقت"""
        now = datetime.now()
        self.datetime_label.config(text=now.strftime("%Y-%m-%d %H:%M:%S"))
        self.root.after(1000, self.update_datetime)
        
    def load_data(self):
        """تحميل البيانات"""
        try:
            self.status_bar.config(text="جاري تحميل البيانات...")
            self.root.update()
            
            # تحميل الإحصائيات
            stats = self.family_service.get_statistics()
            
            self.stats_labels['families'].config(text=str(stats.get('total_families', 0)))
            self.stats_labels['individuals'].config(text=str(stats.get('total_individuals', 0)))
            self.stats_labels['children'].config(text=str(stats.get('children_count', 0)))
            self.stats_labels['adults'].config(text=str(stats.get('adults_count', 0)))
            
            # تحميل الأسر
            self.load_families()
            
            self.status_bar.config(text="تم تحديث البيانات بنجاح")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل البيانات: {str(e)}")
            self.status_bar.config(text="فشل في تحميل البيانات")
            
    def load_families(self):
        """تحميل بيانات الأسر"""
        try:
            # مسح البيانات الحالية
            for item in self.families_tree.get_children():
                self.families_tree.delete(item)
            
            # تحميل الأسر
            families = self.family_service.get_all_families()
            
            for family in families:
                arrival_date = family.arrival_date.strftime('%Y-%m-%d') if family.arrival_date else ""
                
                self.families_tree.insert("", tk.END, values=(
                    family.family_id,
                    family.head_of_family_name,
                    family.head_gender or "",
                    family.phone_number or "",
                    arrival_date,
                    family.tent_number or "",
                    family.get_total_members()
                ))
                
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل الأسر: {str(e)}")
    
    def search_families(self, event=None):
        """البحث في الأسر"""
        search_term = self.search_var.get().strip()
        
        # مسح البيانات الحالية
        for item in self.families_tree.get_children():
            self.families_tree.delete(item)
        
        try:
            if search_term:
                families = self.family_service.search_families(search_term)
            else:
                families = self.family_service.get_all_families()
            
            for family in families:
                arrival_date = family.arrival_date.strftime('%Y-%m-%d') if family.arrival_date else ""
                
                self.families_tree.insert("", tk.END, values=(
                    family.family_id,
                    family.head_of_family_name,
                    family.head_gender or "",
                    family.phone_number or "",
                    arrival_date,
                    family.tent_number or "",
                    family.get_total_members()
                ))
                
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في البحث: {str(e)}")
    
    # الدوال الأخرى (سأضيفها في الجزء التالي)
    def add_new_family(self):
        """إضافة أسرة جديدة"""
        messagebox.showinfo("قيد التطوير", "هذه الميزة قيد التطوير")
    
    def edit_family(self):
        """تعديل أسرة"""
        messagebox.showinfo("قيد التطوير", "هذه الميزة قيد التطوير")
    
    def view_family_details(self, event):
        """عرض تفاصيل الأسرة"""
        messagebox.showinfo("قيد التطوير", "هذه الميزة قيد التطوير")
    
    def export_data(self):
        """تصدير البيانات"""
        self.export_all_families()
    
    def export_all_families(self):
        """تصدير جميع الأسر"""
        try:
            families = self.family_service.get_all_families()
            if not families:
                messagebox.showwarning("تحذير", "لا توجد بيانات للتصدير")
                return
            
            filename = f"families_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            success = self.excel_service.export_families_to_excel(families, filename)
            
            if success:
                messagebox.showinfo("نجح", f"تم تصدير البيانات إلى: {filename}")
            else:
                messagebox.showerror("خطأ", "فشل في تصدير البيانات")
                
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في التصدير: {str(e)}")
    
    def create_excel_template(self):
        """إنشاء قالب Excel"""
        try:
            filename = f"families_template_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            success = self.excel_service.create_families_template(filename)
            
            if success:
                messagebox.showinfo("نجح", f"تم إنشاء القالب: {filename}")
            else:
                messagebox.showerror("خطأ", "فشل في إنشاء القالب")
                
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء القالب: {str(e)}")
    
    def show_statistics_report(self):
        """عرض تقرير الإحصائيات"""
        try:
            stats = self.family_service.get_statistics()
            
            report = f"""تقرير الإحصائيات
{'='*30}

إجمالي الأسر: {stats['total_families']}
إجمالي الأفراد: {stats['total_individuals']}
الأطفال (أقل من 18): {stats['children_count']}
البالغون: {stats['adults_count']}
متوسط حجم الأسرة: {stats['average_family_size']}

تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""
            
            messagebox.showinfo("تقرير الإحصائيات", report)
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء التقرير: {str(e)}")
    
    def logout(self):
        """تسجيل الخروج"""
        if messagebox.askyesno("تسجيل الخروج", "هل أنت متأكد من تسجيل الخروج؟"):
            self.root.destroy()
            from .login_window import LoginWindow
            login_app = LoginWindow()
            login_app.run()
    
    def show_about(self):
        """عرض معلومات حول البرنامج"""
        about_text = """نظام إدارة المخيم
الإصدار 1.0.0

نظام شامل لإدارة مخيمات اللاجئين
يوفر أدوات قوية لإدارة البيانات والتقارير

تم التطوير باستخدام Python و Tkinter"""
        
        messagebox.showinfo("حول البرنامج", about_text)
    
    def run(self):
        """تشغيل النافذة"""
        self.root.mainloop()

if __name__ == "__main__":
    # للاختبار
    from src.services.auth_service import AuthService
    auth = AuthService()
    auth.create_default_admin()
    success, message, user = auth.login("admin", "admin123")
    if success:
        app = MainWindow(user)
        app.run()
