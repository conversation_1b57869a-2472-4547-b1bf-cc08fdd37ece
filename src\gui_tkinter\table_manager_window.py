"""
نافذة إدارة الجداول والأعمدة المتقدمة
Advanced Table and Column Management Window
"""
import tkinter as tk
from tkinter import ttk, messagebox
import json
import sys
import os
from datetime import datetime

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.services.family_service import FamilyService

class TableManagerWindow:
    """نافذة إدارة الجداول والأعمدة"""

    def __init__(self, parent, current_user):
        self.parent = parent
        self.current_user = current_user
        self.family_service = FamilyService()

        # الأعمدة المتاحة مع أنواعها
        self.available_columns = {
            'family_id': {'name': 'رقم الأسرة', 'type': 'text', 'table': 'families'},
            'head_of_family_name': {'name': 'اسم رب الأسرة', 'type': 'text', 'table': 'families'},
            'head_gender': {'name': 'جنس رب الأسرة', 'type': 'choice', 'choices': ['ذكر', 'أنثى'], 'table': 'families'},
            'head_birth_date': {'name': 'تاريخ ميلاد رب الأسرة', 'type': 'date', 'table': 'families'},
            'head_marital_status': {'name': 'الحالة الاجتماعية', 'type': 'choice', 'choices': ['أعزب', 'متزوج', 'مطلق', 'أرمل'], 'table': 'families'},
            'phone_number': {'name': 'رقم الهاتف', 'type': 'text', 'table': 'families'},
            'tent_number': {'name': 'رقم الخيمة', 'type': 'text', 'table': 'families'},
            'arrival_date': {'name': 'تاريخ الوصول', 'type': 'date', 'table': 'families'},
            'nationality': {'name': 'الجنسية', 'type': 'text', 'table': 'families'},
            'origin_country': {'name': 'بلد المنشأ', 'type': 'text', 'table': 'families'},
            'origin_city': {'name': 'مدينة المنشأ', 'type': 'text', 'table': 'families'},
            'family_size': {'name': 'عدد أفراد الأسرة', 'type': 'number', 'table': 'families'},
            'notes': {'name': 'ملاحظات', 'type': 'text', 'table': 'families'},
            'individual_name': {'name': 'اسم الفرد', 'type': 'text', 'table': 'individuals'},
            'individual_gender': {'name': 'جنس الفرد', 'type': 'choice', 'choices': ['ذكر', 'أنثى'], 'table': 'individuals'},
            'individual_birth_date': {'name': 'تاريخ ميلاد الفرد', 'type': 'date', 'table': 'individuals'},
            'relationship_to_head': {'name': 'صلة القرابة', 'type': 'text', 'table': 'individuals'}
        }

        self.selected_columns = []
        self.custom_tables = []

        self.window = tk.Toplevel(parent)
        self.setup_window()
        self.create_widgets()
        self.load_custom_tables()

    def setup_window(self):
        """إعداد النافذة"""
        self.window.title("إدارة الجداول والأعمدة")
        self.window.geometry("1000x700")
        self.window.resizable(True, True)

        # توسيط النافذة
        self.center_window()

        # جعل النافذة modal
        self.window.transient(self.parent)
        self.window.grab_set()

    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = 1000
        height = 700
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان
        title_label = ttk.Label(self.window, text="إدارة الجداول والأعمدة المخصصة",
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=10)

        # إنشاء التبويبات
        self.notebook = ttk.Notebook(self.window)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # تبويب إنشاء جدول جديد
        self.create_new_table_tab()

        # تبويب الجداول المحفوظة
        self.create_saved_tables_tab()

        # أزرار التحكم
        self.create_control_buttons()

    def create_new_table_tab(self):
        """إنشاء تبويب الجدول الجديد"""
        new_table_frame = ttk.Frame(self.notebook)
        self.notebook.add(new_table_frame, text="إنشاء جدول جديد")

        # إطار اليسار - الأعمدة المتاحة
        left_frame = ttk.LabelFrame(new_table_frame, text="الأعمدة المتاحة", padding="10")
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 5), pady=10)

        # شريط البحث
        search_frame = ttk.Frame(left_frame)
        search_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(search_frame, text="البحث:").pack(side=tk.LEFT)
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=20)
        search_entry.pack(side=tk.LEFT, padx=(5, 0), fill=tk.X, expand=True)
        search_entry.bind('<KeyRelease>', self.filter_available_columns)

        # قائمة الأعمدة المتاحة
        self.available_listbox = tk.Listbox(left_frame, height=15)
        self.available_listbox.pack(fill=tk.BOTH, expand=True)

        # تعبئة الأعمدة المتاحة
        self.populate_available_columns()

        # أزرار الوسط
        middle_frame = ttk.Frame(new_table_frame)
        middle_frame.pack(side=tk.LEFT, padx=10, pady=10)

        ttk.Button(middle_frame, text="إضافة →",
                  command=self.add_column, width=12).pack(pady=5)
        ttk.Button(middle_frame, text="← إزالة",
                  command=self.remove_column, width=12).pack(pady=5)
        ttk.Button(middle_frame, text="↑ رفع",
                  command=self.move_column_up, width=12).pack(pady=5)
        ttk.Button(middle_frame, text="↓ خفض",
                  command=self.move_column_down, width=12).pack(pady=5)

        ttk.Separator(middle_frame, orient='horizontal').pack(fill=tk.X, pady=10)

        ttk.Button(middle_frame, text="➕ عمود جديد",
                  command=self.add_custom_column, width=12).pack(pady=5)
        ttk.Button(middle_frame, text="✏️ تعديل عمود",
                  command=self.edit_custom_column, width=12).pack(pady=5)

        # إطار اليمين - الأعمدة المحددة
        right_frame = ttk.LabelFrame(new_table_frame, text="الأعمدة المحددة", padding="10")
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 10), pady=10)

        # معلومات الجدول
        info_frame = ttk.Frame(right_frame)
        info_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(info_frame, text="اسم الجدول:").pack(side=tk.LEFT)
        self.table_name_var = tk.StringVar()
        ttk.Entry(info_frame, textvariable=self.table_name_var, width=20).pack(side=tk.LEFT, padx=(5, 0))

        # قائمة الأعمدة المحددة
        self.selected_listbox = tk.Listbox(right_frame, height=15)
        self.selected_listbox.pack(fill=tk.BOTH, expand=True)

        # أزرار العمليات
        operations_frame = ttk.Frame(right_frame)
        operations_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(operations_frame, text="معاينة الجدول",
                  command=self.preview_table).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(operations_frame, text="حفظ الجدول",
                  command=self.save_custom_table).pack(side=tk.LEFT, padx=5)
        ttk.Button(operations_frame, text="تصدير إلى Excel",
                  command=self.export_custom_table).pack(side=tk.LEFT, padx=5)

    def create_saved_tables_tab(self):
        """إنشاء تبويب الجداول المحفوظة"""
        saved_tables_frame = ttk.Frame(self.notebook)
        self.notebook.add(saved_tables_frame, text="الجداول المحفوظة")

        # شريط الأدوات
        toolbar_frame = ttk.Frame(saved_tables_frame)
        toolbar_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(toolbar_frame, text="تحديث",
                  command=self.load_custom_tables).pack(side=tk.LEFT, padx=5)
        ttk.Button(toolbar_frame, text="حذف جدول",
                  command=self.delete_custom_table).pack(side=tk.LEFT, padx=5)
        ttk.Button(toolbar_frame, text="تحميل جدول",
                  command=self.load_custom_table).pack(side=tk.LEFT, padx=5)

        # قائمة الجداول المحفوظة
        self.saved_tables_tree = ttk.Treeview(saved_tables_frame,
                                             columns=("name", "columns_count", "created_date"),
                                             show="headings", height=15)

        self.saved_tables_tree.heading("name", text="اسم الجدول")
        self.saved_tables_tree.heading("columns_count", text="عدد الأعمدة")
        self.saved_tables_tree.heading("created_date", text="تاريخ الإنشاء")

        self.saved_tables_tree.column("name", width=200)
        self.saved_tables_tree.column("columns_count", width=100)
        self.saved_tables_tree.column("created_date", width=150)

        self.saved_tables_tree.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # ربط النقر المزدوج
        self.saved_tables_tree.bind("<Double-1>", lambda e: self.load_custom_table())

    def create_control_buttons(self):
        """إنشاء أزرار التحكم"""
        buttons_frame = ttk.Frame(self.window)
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Button(buttons_frame, text="إغلاق",
                  command=self.window.destroy).pack(side=tk.RIGHT, padx=5)

        ttk.Button(buttons_frame, text="مساعدة",
                  command=self.show_help).pack(side=tk.LEFT, padx=5)

    def populate_available_columns(self):
        """تعبئة قائمة الأعمدة المتاحة"""
        self.available_listbox.delete(0, tk.END)

        for column_id, column_info in self.available_columns.items():
            display_text = f"{column_info['name']} ({column_info['type']})"
            self.available_listbox.insert(tk.END, display_text)

    def filter_available_columns(self, event=None):
        """تصفية الأعمدة المتاحة"""
        search_term = self.search_var.get().lower()
        self.available_listbox.delete(0, tk.END)

        for column_id, column_info in self.available_columns.items():
            if search_term in column_info['name'].lower():
                display_text = f"{column_info['name']} ({column_info['type']})"
                self.available_listbox.insert(tk.END, display_text)

    def add_column(self):
        """إضافة عمود للجدول المخصص"""
        selection = self.available_listbox.curselection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار عمود لإضافته")
            return

        selected_text = self.available_listbox.get(selection[0])
        column_name = selected_text.split(" (")[0]

        # البحث عن معرف العمود
        column_id = None
        for col_id, col_info in self.available_columns.items():
            if col_info['name'] == column_name:
                column_id = col_id
                break

        if column_id and column_id not in self.selected_columns:
            self.selected_columns.append(column_id)
            self.update_selected_listbox()

    def remove_column(self):
        """إزالة عمود من الجدول المخصص"""
        selection = self.selected_listbox.curselection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار عمود لإزالته")
            return

        index = selection[0]
        if 0 <= index < len(self.selected_columns):
            del self.selected_columns[index]
            self.update_selected_listbox()

    def move_column_up(self):
        """رفع عمود في الترتيب"""
        selection = self.selected_listbox.curselection()
        if not selection:
            return

        index = selection[0]
        if index > 0:
            self.selected_columns[index], self.selected_columns[index-1] = \
                self.selected_columns[index-1], self.selected_columns[index]
            self.update_selected_listbox()
            self.selected_listbox.selection_set(index-1)

    def move_column_down(self):
        """خفض عمود في الترتيب"""
        selection = self.selected_listbox.curselection()
        if not selection:
            return

        index = selection[0]
        if index < len(self.selected_columns) - 1:
            self.selected_columns[index], self.selected_columns[index+1] = \
                self.selected_columns[index+1], self.selected_columns[index]
            self.update_selected_listbox()
            self.selected_listbox.selection_set(index+1)

    def update_selected_listbox(self):
        """تحديث قائمة الأعمدة المحددة"""
        self.selected_listbox.delete(0, tk.END)

        for column_id in self.selected_columns:
            if column_id in self.available_columns:
                column_info = self.available_columns[column_id]
                display_text = f"{column_info['name']} ({column_info['type']})"
                self.selected_listbox.insert(tk.END, display_text)

    def preview_table(self):
        """معاينة الجدول المخصص"""
        if not self.selected_columns:
            messagebox.showwarning("تحذير", "يرجى اختيار أعمدة للجدول")
            return

        # إنشاء نافذة المعاينة
        preview_window = tk.Toplevel(self.window)
        preview_window.title("معاينة الجدول")
        preview_window.geometry("800x500")

        # إنشاء جدول المعاينة
        columns = [col_id for col_id in self.selected_columns]
        tree = ttk.Treeview(preview_window, columns=columns, show="headings")

        # تعيين عناوين الأعمدة
        for col_id in columns:
            if col_id in self.available_columns:
                tree.heading(col_id, text=self.available_columns[col_id]['name'])
                tree.column(col_id, width=150)

        # إضافة بيانات تجريبية
        families = self.family_service.get_all_families()[:10]  # أول 10 أسر
        for family in families:
            values = []
            for col_id in columns:
                value = self.get_column_value(family, col_id)
                values.append(str(value) if value is not None else "")
            tree.insert("", tk.END, values=values)

        tree.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # زر إغلاق
        ttk.Button(preview_window, text="إغلاق",
                  command=preview_window.destroy).pack(pady=10)

    def get_column_value(self, family, column_id):
        """الحصول على قيمة العمود للأسرة"""
        if column_id == 'family_size':
            return family.get_total_members()
        elif hasattr(family, column_id):
            return getattr(family, column_id)
        elif column_id.startswith('individual_'):
            # للأعمدة المتعلقة بالأفراد، نأخذ أول فرد
            if family.individuals:
                individual = family.individuals[0]
                field_name = column_id.replace('individual_', '')
                if field_name == 'name':
                    return individual.full_name
                elif hasattr(individual, field_name):
                    return getattr(individual, field_name)
        return None

    def save_custom_table(self):
        """حفظ الجدول المخصص"""
        if not self.table_name_var.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال اسم للجدول")
            return

        if not self.selected_columns:
            messagebox.showerror("خطأ", "يرجى اختيار أعمدة للجدول")
            return

        table_data = {
            'name': self.table_name_var.get().strip(),
            'columns': self.selected_columns,
            'created_date': str(datetime.now())
        }

        # حفظ في ملف JSON
        try:
            tables_file = "data/custom_tables.json"
            os.makedirs(os.path.dirname(tables_file), exist_ok=True)

            # تحميل الجداول الموجودة
            existing_tables = []
            if os.path.exists(tables_file):
                with open(tables_file, 'r', encoding='utf-8') as f:
                    existing_tables = json.load(f)

            # إضافة الجدول الجديد
            existing_tables.append(table_data)

            # حفظ الملف
            with open(tables_file, 'w', encoding='utf-8') as f:
                json.dump(existing_tables, f, ensure_ascii=False, indent=2)

            messagebox.showinfo("نجح", f"تم حفظ الجدول '{table_data['name']}' بنجاح")
            self.load_custom_tables()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ الجدول: {str(e)}")

    def load_custom_tables(self):
        """تحميل الجداول المخصصة المحفوظة"""
        try:
            tables_file = "data/custom_tables.json"
            if not os.path.exists(tables_file):
                return

            with open(tables_file, 'r', encoding='utf-8') as f:
                self.custom_tables = json.load(f)

            # تحديث جدول الجداول المحفوظة
            for item in self.saved_tables_tree.get_children():
                self.saved_tables_tree.delete(item)

            for table in self.custom_tables:
                self.saved_tables_tree.insert("", tk.END, values=(
                    table['name'],
                    len(table['columns']),
                    table['created_date'][:10]  # التاريخ فقط
                ))

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل الجداول: {str(e)}")

    def delete_custom_table(self):
        """حذف جدول مخصص"""
        selection = self.saved_tables_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار جدول للحذف")
            return

        if messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف هذا الجدول؟"):
            messagebox.showinfo("قيد التطوير", "ميزة حذف الجدول قيد التطوير")

    def load_custom_table(self):
        """تحميل جدول مخصص للتعديل"""
        selection = self.saved_tables_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار جدول للتحميل")
            return

        try:
            # الحصول على اسم الجدول المحدد
            item = self.saved_tables_tree.item(selection[0])
            table_name = item['values'][0]

            # البحث عن الجدول في القائمة
            selected_table = None
            for table in self.custom_tables:
                if table['name'] == table_name:
                    selected_table = table
                    break

            if not selected_table:
                messagebox.showerror("خطأ", "لم يتم العثور على الجدول")
                return

            # تحميل بيانات الجدول
            self.table_name_var.set(selected_table['name'])
            self.selected_columns = selected_table['columns'].copy()

            # تحديث قائمة الأعمدة المحددة
            self.update_selected_listbox()

            # التبديل إلى تبويب إنشاء جدول جديد
            self.notebook.select(0)

            messagebox.showinfo("نجح", f"تم تحميل الجدول '{table_name}' بنجاح")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل الجدول: {str(e)}")

    def export_custom_table(self):
        """تصدير الجدول المخصص إلى Excel"""
        if not self.selected_columns:
            messagebox.showwarning("تحذير", "يرجى اختيار أعمدة للتصدير")
            return

        messagebox.showinfo("قيد التطوير", "ميزة التصدير قيد التطوير")

    def show_help(self):
        """عرض المساعدة"""
        help_text = """مساعدة إدارة الجداول والأعمدة

1. إنشاء جدول جديد:
   - اختر الأعمدة من القائمة اليسرى
   - أضفها للقائمة اليمنى
   - رتب الأعمدة حسب الحاجة
   - أدخل اسم للجدول
   - احفظ الجدول

2. أنواع الأعمدة:
   - نص: للنصوص العادية
   - رقم: للأرقام
   - تاريخ: للتواريخ
   - اختيار: قائمة خيارات محددة

3. العمليات المتاحة:
   - معاينة الجدول
   - حفظ الجدول
   - تصدير إلى Excel
   - إدارة الجداول المحفوظة"""

        messagebox.showinfo("مساعدة", help_text)

    def add_custom_column(self):
        """إضافة عمود مخصص جديد"""
        self.show_column_editor()

    def edit_custom_column(self):
        """تعديل عمود مخصص"""
        selection = self.selected_listbox.curselection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار عمود للتعديل")
            return

        index = selection[0]
        if 0 <= index < len(self.selected_columns):
            column_id = self.selected_columns[index]
            if column_id in self.available_columns:
                column_info = self.available_columns[column_id]
                self.show_column_editor(column_id, column_info)

    def show_column_editor(self, column_id=None, column_info=None):
        """عرض محرر الأعمدة"""
        # إنشاء نافذة المحرر
        editor_window = tk.Toplevel(self.window)
        editor_window.title("محرر الأعمدة" if not column_id else "تعديل العمود")
        editor_window.geometry("500x400")
        editor_window.resizable(False, False)

        # توسيط النافذة
        editor_window.update_idletasks()
        width = 500
        height = 400
        x = (editor_window.winfo_screenwidth() // 2) - (width // 2)
        y = (editor_window.winfo_screenheight() // 2) - (height // 2)
        editor_window.geometry(f"{width}x{height}+{x}+{y}")

        # جعل النافذة modal
        editor_window.transient(self.window)
        editor_window.grab_set()

        # إطار رئيسي
        main_frame = ttk.Frame(editor_window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # العنوان
        title = "إضافة عمود جديد" if not column_id else f"تعديل العمود: {column_info['name']}"
        ttk.Label(main_frame, text=title, font=("Arial", 14, "bold")).pack(pady=(0, 20))

        # معرف العمود
        ttk.Label(main_frame, text="معرف العمود:", font=("Arial", 10, "bold")).pack(anchor=tk.W)
        column_id_var = tk.StringVar(value=column_id if column_id else "")
        column_id_entry = ttk.Entry(main_frame, textvariable=column_id_var, width=40)
        column_id_entry.pack(fill=tk.X, pady=(5, 10))
        if column_id:  # في حالة التعديل، منع تغيير المعرف
            column_id_entry.config(state="readonly")

        # اسم العمود
        ttk.Label(main_frame, text="اسم العمود:", font=("Arial", 10, "bold")).pack(anchor=tk.W)
        column_name_var = tk.StringVar(value=column_info['name'] if column_info else "")
        ttk.Entry(main_frame, textvariable=column_name_var, width=40).pack(fill=tk.X, pady=(5, 10))

        # نوع البيانات
        ttk.Label(main_frame, text="نوع البيانات:", font=("Arial", 10, "bold")).pack(anchor=tk.W)
        column_type_var = tk.StringVar(value=column_info['type'] if column_info else "text")
        type_combo = ttk.Combobox(main_frame, textvariable=column_type_var, width=37, state="readonly")
        type_combo['values'] = ["text", "number", "date", "choice", "boolean"]
        type_combo.pack(fill=tk.X, pady=(5, 10))
        type_combo.bind('<<ComboboxSelected>>', lambda e: self.on_type_change(e, choices_frame, choices_text))

        # خيارات الاختيار (للنوع choice)
        choices_frame = ttk.Frame(main_frame)
        choices_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(choices_frame, text="خيارات الاختيار (مفصولة بفاصلة):", font=("Arial", 10, "bold")).pack(anchor=tk.W)
        choices_text = tk.Text(choices_frame, height=3, width=40)
        choices_text.pack(fill=tk.X, pady=(5, 0))

        # تعبئة الخيارات إذا كان النوع choice
        if column_info and column_info.get('type') == 'choice' and column_info.get('choices'):
            choices_text.insert(1.0, ', '.join(column_info['choices']))

        # إخفاء/إظهار خيارات الاختيار حسب النوع
        self.toggle_choices_visibility(column_type_var.get(), choices_frame)

        # الجدول المرتبط
        ttk.Label(main_frame, text="الجدول المرتبط:", font=("Arial", 10, "bold")).pack(anchor=tk.W)
        table_var = tk.StringVar(value=column_info.get('table', 'families') if column_info else "families")
        table_combo = ttk.Combobox(main_frame, textvariable=table_var, width=37, state="readonly")
        table_combo['values'] = ["families", "individuals", "aid_distributions", "custom"]
        table_combo.pack(fill=tk.X, pady=(5, 10))

        # أزرار
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(20, 0))

        def save_column():
            # التحقق من البيانات
            if not column_id_var.get().strip():
                messagebox.showerror("خطأ", "يرجى إدخال معرف العمود")
                return

            if not column_name_var.get().strip():
                messagebox.showerror("خطأ", "يرجى إدخال اسم العمود")
                return

            # تحضير بيانات العمود
            new_column_info = {
                'name': column_name_var.get().strip(),
                'type': column_type_var.get(),
                'table': table_var.get()
            }

            # إضافة الخيارات إذا كان النوع choice
            if column_type_var.get() == 'choice':
                choices_str = choices_text.get(1.0, tk.END).strip()
                if choices_str:
                    choices = [choice.strip() for choice in choices_str.split(',') if choice.strip()]
                    new_column_info['choices'] = choices
                else:
                    messagebox.showerror("خطأ", "يرجى إدخال خيارات الاختيار")
                    return

            # حفظ العمود
            new_column_id = column_id_var.get().strip()

            if not column_id:  # إضافة عمود جديد
                if new_column_id in self.available_columns:
                    messagebox.showerror("خطأ", "معرف العمود موجود بالفعل")
                    return

                self.available_columns[new_column_id] = new_column_info
                messagebox.showinfo("نجح", "تم إضافة العمود بنجاح")
            else:  # تعديل عمود موجود
                self.available_columns[column_id] = new_column_info
                messagebox.showinfo("نجح", "تم تعديل العمود بنجاح")

            # تحديث القوائم
            self.populate_available_columns()
            self.update_selected_listbox()

            editor_window.destroy()

        ttk.Button(buttons_frame, text="حفظ", command=save_column).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="إلغاء", command=editor_window.destroy).pack(side=tk.LEFT)

    def on_type_change(self, event, choices_frame, choices_text):
        """عند تغيير نوع البيانات"""
        column_type = event.widget.get()
        self.toggle_choices_visibility(column_type, choices_frame)

    def toggle_choices_visibility(self, column_type, choices_frame):
        """إظهار/إخفاء خيارات الاختيار"""
        if column_type == 'choice':
            choices_frame.pack(fill=tk.X, pady=(0, 10))
        else:
            choices_frame.pack_forget()

if __name__ == "__main__":
    # للاختبار
    root = tk.Tk()
    root.withdraw()

    from src.services.auth_service import AuthService
    auth = AuthService()
    auth.create_default_admin()
    success, message, user = auth.login("admin", "admin123")

    if success:
        app = TableManagerWindow(root, user)
        root.mainloop()
