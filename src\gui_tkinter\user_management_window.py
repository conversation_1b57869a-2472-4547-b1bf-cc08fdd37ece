"""
نافذة إدارة المستخدمين
User Management Window
"""
import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.services.auth_service import AuthService
from src.config.settings import USER_ROLES, ROLE_DESCRIPTIONS

class UserManagementWindow:
    """نافذة إدارة المستخدمين"""
    
    def __init__(self, parent, current_user):
        self.parent = parent
        self.current_user = current_user
        self.auth_service = AuthService()
        self.auth_service.current_user = current_user
        
        # التحقق من الصلاحيات
        if not self.auth_service.can_manage_users():
            messagebox.showerror("خطأ", "ليس لديك صلاحية لإدارة المستخدمين")
            return
        
        self.window = tk.Toplevel(parent)
        self.setup_window()
        self.create_widgets()
        self.load_users()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.window.title("إدارة المستخدمين")
        self.window.geometry("900x600")
        self.window.resizable(True, True)
        
        # توسيط النافذة
        self.center_window()
        
        # جعل النافذة modal
        self.window.transient(self.parent)
        self.window.grab_set()
        
    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = 900
        height = 600
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
        
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان
        title_frame = ttk.Frame(self.window)
        title_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(title_frame, text="إدارة المستخدمين", 
                 font=("Arial", 16, "bold")).pack()
        
        # شريط الأدوات
        self.create_toolbar()
        
        # جدول المستخدمين
        self.create_users_table()
        
        # أزرار التحكم
        self.create_control_buttons()
        
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar_frame = ttk.Frame(self.window)
        toolbar_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # البحث
        ttk.Label(toolbar_frame, text="البحث:").pack(side=tk.LEFT, padx=(0, 5))
        
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(toolbar_frame, textvariable=self.search_var, width=30)
        search_entry.pack(side=tk.LEFT, padx=(0, 10))
        search_entry.bind('<KeyRelease>', self.search_users)
        
        # أزرار
        if self.auth_service.can_create_user():
            ttk.Button(toolbar_frame, text="إضافة مستخدم", 
                      command=self.add_user).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(toolbar_frame, text="تعديل", 
                  command=self.edit_user).pack(side=tk.LEFT, padx=5)
        
        if self.auth_service.can_delete_user():
            ttk.Button(toolbar_frame, text="حذف", 
                      command=self.delete_user).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(toolbar_frame, text="تحديث", 
                  command=self.load_users).pack(side=tk.LEFT, padx=5)
        
    def create_users_table(self):
        """إنشاء جدول المستخدمين"""
        # إطار الجدول
        table_frame = ttk.Frame(self.window)
        table_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # أعمدة الجدول
        columns = ("username", "full_name", "email", "role", "created_at", "last_login")
        
        self.users_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)
        
        # تعيين عناوين الأعمدة
        self.users_tree.heading("username", text="اسم المستخدم")
        self.users_tree.heading("full_name", text="الاسم الكامل")
        self.users_tree.heading("email", text="البريد الإلكتروني")
        self.users_tree.heading("role", text="الدور")
        self.users_tree.heading("created_at", text="تاريخ الإنشاء")
        self.users_tree.heading("last_login", text="آخر تسجيل دخول")
        
        # تعيين عرض الأعمدة
        self.users_tree.column("username", width=120)
        self.users_tree.column("full_name", width=150)
        self.users_tree.column("email", width=180)
        self.users_tree.column("role", width=100)
        self.users_tree.column("created_at", width=120)
        self.users_tree.column("last_login", width=120)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.users_tree.yview)
        self.users_tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.users_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط النقر المزدوج
        self.users_tree.bind("<Double-1>", self.on_user_double_click)
        
    def create_control_buttons(self):
        """إنشاء أزرار التحكم"""
        buttons_frame = ttk.Frame(self.window)
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(buttons_frame, text="إغلاق", 
                  command=self.window.destroy).pack(side=tk.RIGHT, padx=5)
        
        ttk.Button(buttons_frame, text="تصدير قائمة المستخدمين", 
                  command=self.export_users).pack(side=tk.LEFT, padx=5)
        
    def load_users(self):
        """تحميل قائمة المستخدمين"""
        try:
            # مسح البيانات الحالية
            for item in self.users_tree.get_children():
                self.users_tree.delete(item)
            
            # تحميل المستخدمين
            users = self.auth_service.get_all_users()
            
            for user in users:
                created_at = user.created_at.strftime('%Y-%m-%d') if user.created_at else ""
                last_login = user.last_login.strftime('%Y-%m-%d %H:%M') if user.last_login else "لم يسجل دخول"
                role_display = USER_ROLES.get(user.role, user.role)
                
                self.users_tree.insert("", tk.END, values=(
                    user.username,
                    user.full_name,
                    user.email or "",
                    role_display,
                    created_at,
                    last_login
                ), tags=(user.id,))
                
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل المستخدمين: {str(e)}")
    
    def search_users(self, event=None):
        """البحث في المستخدمين"""
        search_term = self.search_var.get().strip().lower()
        
        # مسح البيانات الحالية
        for item in self.users_tree.get_children():
            self.users_tree.delete(item)
        
        try:
            users = self.auth_service.get_all_users()
            
            for user in users:
                # البحث في الاسم أو اسم المستخدم أو البريد الإلكتروني
                if (not search_term or 
                    search_term in user.username.lower() or
                    search_term in user.full_name.lower() or
                    (user.email and search_term in user.email.lower())):
                    
                    created_at = user.created_at.strftime('%Y-%m-%d') if user.created_at else ""
                    last_login = user.last_login.strftime('%Y-%m-%d %H:%M') if user.last_login else "لم يسجل دخول"
                    role_display = USER_ROLES.get(user.role, user.role)
                    
                    self.users_tree.insert("", tk.END, values=(
                        user.username,
                        user.full_name,
                        user.email or "",
                        role_display,
                        created_at,
                        last_login
                    ), tags=(user.id,))
                    
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في البحث: {str(e)}")
    
    def get_selected_user_id(self):
        """الحصول على معرف المستخدم المحدد"""
        selection = self.users_tree.selection()
        if not selection:
            return None
        
        item = self.users_tree.item(selection[0])
        tags = item.get('tags', [])
        return tags[0] if tags else None
    
    def add_user(self):
        """إضافة مستخدم جديد"""
        dialog = UserDialog(self.window, "إضافة مستخدم جديد")
        if dialog.result:
            try:
                success, message = self.auth_service.create_user(
                    dialog.result['username'],
                    dialog.result['password'],
                    dialog.result['full_name'],
                    dialog.result['email'],
                    dialog.result['role']
                )
                
                if success:
                    messagebox.showinfo("نجح", message)
                    self.load_users()
                else:
                    messagebox.showerror("خطأ", message)
                    
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في إضافة المستخدم: {str(e)}")
    
    def edit_user(self):
        """تعديل مستخدم"""
        user_id = self.get_selected_user_id()
        if not user_id:
            messagebox.showwarning("تحذير", "يرجى اختيار مستخدم للتعديل")
            return
        
        messagebox.showinfo("قيد التطوير", "ميزة تعديل المستخدم قيد التطوير")
    
    def delete_user(self):
        """حذف مستخدم"""
        user_id = self.get_selected_user_id()
        if not user_id:
            messagebox.showwarning("تحذير", "يرجى اختيار مستخدم للحذف")
            return
        
        if messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف هذا المستخدم؟"):
            messagebox.showinfo("قيد التطوير", "ميزة حذف المستخدم قيد التطوير")
    
    def on_user_double_click(self, event):
        """معالجة النقر المزدوج على مستخدم"""
        self.edit_user()
    
    def export_users(self):
        """تصدير قائمة المستخدمين"""
        messagebox.showinfo("قيد التطوير", "ميزة تصدير المستخدمين قيد التطوير")

class UserDialog:
    """حوار إضافة/تعديل مستخدم"""
    
    def __init__(self, parent, title, user_data=None):
        self.parent = parent
        self.user_data = user_data
        self.result = None
        
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("400x500")
        self.dialog.resizable(False, False)
        
        # توسيط النافذة
        self.center_dialog()
        
        # جعل النافذة modal
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        self.create_widgets()
        
        # انتظار إغلاق النافذة
        self.dialog.wait_window()
    
    def center_dialog(self):
        """توسيط النافذة"""
        self.dialog.update_idletasks()
        width = 400
        height = 500
        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        self.dialog.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_widgets(self):
        """إنشاء عناصر الحوار"""
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # اسم المستخدم
        ttk.Label(main_frame, text="اسم المستخدم *:", font=("Arial", 10, "bold")).pack(anchor=tk.W, pady=(0, 5))
        self.username_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.username_var, width=40).pack(fill=tk.X, pady=(0, 15))
        
        # كلمة المرور
        ttk.Label(main_frame, text="كلمة المرور *:", font=("Arial", 10, "bold")).pack(anchor=tk.W, pady=(0, 5))
        self.password_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.password_var, show="*", width=40).pack(fill=tk.X, pady=(0, 15))
        
        # الاسم الكامل
        ttk.Label(main_frame, text="الاسم الكامل *:", font=("Arial", 10, "bold")).pack(anchor=tk.W, pady=(0, 5))
        self.full_name_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.full_name_var, width=40).pack(fill=tk.X, pady=(0, 15))
        
        # البريد الإلكتروني
        ttk.Label(main_frame, text="البريد الإلكتروني:", font=("Arial", 10, "bold")).pack(anchor=tk.W, pady=(0, 5))
        self.email_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.email_var, width=40).pack(fill=tk.X, pady=(0, 15))
        
        # الدور
        ttk.Label(main_frame, text="الدور *:", font=("Arial", 10, "bold")).pack(anchor=tk.W, pady=(0, 5))
        self.role_var = tk.StringVar()
        role_combo = ttk.Combobox(main_frame, textvariable=self.role_var, width=37, state="readonly")
        role_combo['values'] = list(USER_ROLES.keys())
        role_combo.pack(fill=tk.X, pady=(0, 15))
        
        # وصف الدور
        self.role_desc_label = ttk.Label(main_frame, text="", font=("Arial", 9), foreground="gray")
        self.role_desc_label.pack(anchor=tk.W, pady=(0, 15))
        
        # ربط تغيير الدور بعرض الوصف
        role_combo.bind('<<ComboboxSelected>>', self.on_role_change)
        
        # ملاحظة
        ttk.Label(main_frame, text="* حقول مطلوبة", font=("Arial", 9), foreground="red").pack(anchor=tk.W, pady=(10, 0))
        
        # أزرار
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(20, 0))
        
        ttk.Button(buttons_frame, text="حفظ", command=self.save_user).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="إلغاء", command=self.dialog.destroy).pack(side=tk.LEFT)
        
        # تعبئة البيانات إذا كان تعديل
        if self.user_data:
            self.username_var.set(self.user_data.get('username', ''))
            self.full_name_var.set(self.user_data.get('full_name', ''))
            self.email_var.set(self.user_data.get('email', ''))
            self.role_var.set(self.user_data.get('role', ''))
            self.on_role_change()
    
    def on_role_change(self, event=None):
        """معالجة تغيير الدور"""
        role = self.role_var.get()
        description = ROLE_DESCRIPTIONS.get(role, "")
        self.role_desc_label.config(text=description)
    
    def save_user(self):
        """حفظ بيانات المستخدم"""
        # التحقق من الحقول المطلوبة
        if not self.username_var.get().strip():
            messagebox.showerror("خطأ", "اسم المستخدم مطلوب")
            return
        
        if not self.password_var.get():
            messagebox.showerror("خطأ", "كلمة المرور مطلوبة")
            return
        
        if not self.full_name_var.get().strip():
            messagebox.showerror("خطأ", "الاسم الكامل مطلوب")
            return
        
        if not self.role_var.get():
            messagebox.showerror("خطأ", "يرجى اختيار الدور")
            return
        
        # حفظ النتيجة
        self.result = {
            'username': self.username_var.get().strip(),
            'password': self.password_var.get(),
            'full_name': self.full_name_var.get().strip(),
            'email': self.email_var.get().strip() or None,
            'role': self.role_var.get()
        }
        
        self.dialog.destroy()

if __name__ == "__main__":
    # للاختبار
    root = tk.Tk()
    root.withdraw()  # إخفاء النافذة الرئيسية
    
    from src.services.auth_service import AuthService
    auth = AuthService()
    auth.create_default_admin()
    success, message, user = auth.login("admin", "admin123")
    
    if success:
        app = UserManagementWindow(root, user)
        root.mainloop()
