"""
الملف الرئيسي لتطبيق نظام إدارة المخيم
Main Application Entry Point
"""
import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from PyQt5.QtWidgets import QApplication, QMessageBox, QSplashScreen
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QPixmap, QFont

# استيراد الوحدات المطلوبة
from src.config.database import create_tables, init_database
from src.config.settings import create_directories
from src.gui.login_window import LoginWindow
from src.gui.main_window import MainWindow
from src.services.auth_service import AuthService

class CampManagementApp:
    """التطبيق الرئيسي لنظام إدارة المخيم"""
    
    def __init__(self):
        self.app = None
        self.login_window = None
        self.main_window = None
        self.current_user = None
        
    def initialize_app(self):
        """تهيئة التطبيق"""
        # إنشاء تطبيق Qt
        self.app = QApplication(sys.argv)
        self.app.setApplicationName("نظام إدارة المخيم")
        self.app.setApplicationVersion("1.0.0")
        self.app.setOrganizationName("Camp Management Team")
        
        # تعيين الخط الافتراضي
        font = QFont("Arial", 10)
        self.app.setFont(font)
        
        # تعيين اتجاه النص (RTL للعربية)
        self.app.setLayoutDirection(Qt.RightToLeft)
        
        return True
        
    def setup_database(self):
        """إعداد قاعدة البيانات"""
        try:
            # إنشاء المجلدات المطلوبة
            create_directories()
            
            # تهيئة قاعدة البيانات
            init_database()
            
            # إنشاء الجداول
            create_tables()
            
            return True
            
        except Exception as e:
            QMessageBox.critical(
                None, 
                "خطأ في قاعدة البيانات",
                f"فشل في إعداد قاعدة البيانات:\n{str(e)}\n\n"
                "يرجى التأكد من صلاحيات الكتابة في مجلد التطبيق."
            )
            return False
            
    def show_splash_screen(self):
        """إظهار شاشة البداية"""
        try:
            # إنشاء شاشة البداية
            splash_pixmap = QPixmap(400, 300)
            splash_pixmap.fill(Qt.white)
            
            splash = QSplashScreen(splash_pixmap)
            splash.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.SplashScreen)
            
            # إضافة نص إلى شاشة البداية
            splash.showMessage(
                "نظام إدارة المخيم\nCamp Management System\n\nجاري التحميل...",
                Qt.AlignCenter | Qt.AlignBottom,
                Qt.black
            )
            
            splash.show()
            self.app.processEvents()
            
            # إخفاء الشاشة بعد 3 ثوان
            QTimer.singleShot(3000, splash.close)
            
            return splash
            
        except Exception as e:
            print(f"تحذير: فشل في إظهار شاشة البداية: {str(e)}")
            return None
            
    def show_login_window(self):
        """إظهار نافذة تسجيل الدخول"""
        try:
            self.login_window = LoginWindow()
            
            # ربط إشارة نجاح تسجيل الدخول
            self.login_window.login_successful.connect(self.on_login_successful)
            
            self.login_window.show()
            return True
            
        except Exception as e:
            QMessageBox.critical(
                None,
                "خطأ",
                f"فشل في إظهار نافذة تسجيل الدخول:\n{str(e)}"
            )
            return False
            
    def on_login_successful(self, user):
        """عند نجاح تسجيل الدخول"""
        try:
            self.current_user = user
            
            # إخفاء نافذة تسجيل الدخول
            if self.login_window:
                self.login_window.hide()
            
            # إظهار النافذة الرئيسية
            self.show_main_window()
            
        except Exception as e:
            QMessageBox.critical(
                None,
                "خطأ",
                f"فشل في فتح النافذة الرئيسية:\n{str(e)}"
            )
            
    def show_main_window(self):
        """إظهار النافذة الرئيسية"""
        try:
            self.main_window = MainWindow(self.current_user)
            self.main_window.show()
            
            # إغلاق نافذة تسجيل الدخول
            if self.login_window:
                self.login_window.close()
                
        except Exception as e:
            QMessageBox.critical(
                None,
                "خطأ",
                f"فشل في إظهار النافذة الرئيسية:\n{str(e)}"
            )
            
    def run(self):
        """تشغيل التطبيق"""
        try:
            # تهيئة التطبيق
            if not self.initialize_app():
                return 1
                
            # إعداد قاعدة البيانات
            if not self.setup_database():
                return 1
                
            # إظهار شاشة البداية
            splash = self.show_splash_screen()
            
            # انتظار قليل لإظهار شاشة البداية
            if splash:
                QTimer.singleShot(3000, self.show_login_window)
            else:
                self.show_login_window()
            
            # تشغيل حلقة الأحداث
            return self.app.exec_()
            
        except KeyboardInterrupt:
            print("\nتم إيقاف التطبيق بواسطة المستخدم")
            return 0
            
        except Exception as e:
            QMessageBox.critical(
                None,
                "خطأ فادح",
                f"حدث خطأ غير متوقع:\n{str(e)}\n\n"
                "سيتم إغلاق التطبيق."
            )
            return 1


def main():
    """الدالة الرئيسية"""
    try:
        # إنشاء وتشغيل التطبيق
        app = CampManagementApp()
        exit_code = app.run()
        
        print(f"تم إنهاء التطبيق برمز الخروج: {exit_code}")
        sys.exit(exit_code)
        
    except Exception as e:
        print(f"خطأ فادح في التطبيق: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
