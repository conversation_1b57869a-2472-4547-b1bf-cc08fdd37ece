"""
نموذج توزيع المساعدات
Aid Distribution Model
"""
from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text, Date, ForeignKey, Float
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from ..config.database import Base

class AidDistribution(Base):
    """نموذج توزيع المساعدات"""
    __tablename__ = "aid_distributions"

    id = Column(Integer, primary_key=True, index=True)
    family_id = Column(Integer, ForeignKey("families.id"), nullable=False)
    distribution_date = Column(Date, nullable=False)  # تاريخ التوزيع
    aid_type = Column(String(50), nullable=False)  # نوع المساعدة
    aid_category = Column(String(30), nullable=False)  # فئة المساعدة (غذائية، طبية، إلخ)
    quantity = Column(Float)  # الكمية
    unit = Column(String(20))  # الوحدة (كيلو، قطعة، إلخ)
    description = Column(Text)  # وصف المساعدة
    distributed_by = Column(String(100))  # اسم الموزع
    received_by = Column(String(100))  # اسم المستلم
    distribution_point = Column(String(50))  # نقطة التوزيع
    batch_number = Column(String(30))  # رقم الدفعة
    expiry_date = Column(Date)  # تاريخ انتهاء الصلاحية (للمواد الغذائية والطبية)
    supplier = Column(String(100))  # المورد
    cost = Column(Float)  # التكلفة
    currency = Column(String(10), default="USD")  # العملة
    is_emergency = Column(Boolean, default=False)  # توزيع طارئ
    status = Column(String(20), default="completed")  # حالة التوزيع
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    notes = Column(Text)  # ملاحظات

    # العلاقات
    # family = relationship("Family", back_populates="aid_distributions")  # سيتم تفعيلها لاحقاً

    def get_status_display(self) -> str:
        """الحصول على حالة التوزيع باللغة العربية"""
        status_map = {
            "pending": "في الانتظار",
            "in_progress": "قيد التنفيذ",
            "completed": "مكتمل",
            "cancelled": "ملغي",
            "delayed": "مؤجل"
        }
        return status_map.get(self.status, self.status)

    def get_aid_category_display(self) -> str:
        """الحصول على فئة المساعدة باللغة العربية"""
        category_map = {
            "food": "غذائية",
            "medical": "طبية",
            "clothing": "ملابس",
            "shelter": "مأوى",
            "hygiene": "نظافة شخصية",
            "education": "تعليمية",
            "cash": "نقدية",
            "other": "أخرى"
        }
        return category_map.get(self.aid_category, self.aid_category)

    def is_expired(self) -> bool:
        """التحقق من انتهاء صلاحية المساعدة"""
        if not self.expiry_date:
            return False

        from datetime import date
        return date.today() > self.expiry_date

    def days_until_expiry(self) -> int:
        """عدد الأيام المتبقية حتى انتهاء الصلاحية"""
        if not self.expiry_date:
            return -1

        from datetime import date
        delta = self.expiry_date - date.today()
        return delta.days

    def __repr__(self):
        return f"<AidDistribution(aid_type='{self.aid_type}', date='{self.distribution_date}')>"
