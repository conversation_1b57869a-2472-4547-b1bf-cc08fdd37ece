"""
نموذج الأسر
Family Model
"""
from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text, Date
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from ..config.database import Base

class Family(Base):
    """نموذج الأسرة"""
    __tablename__ = "families"

    id = Column(Integer, primary_key=True, index=True)
    family_id = Column(String(20), unique=True, index=True, nullable=False)  # رقم تعريف الأسرة
    head_of_family_name = Column(String(100), nullable=False)  # اسم رب الأسرة
    head_birth_date = Column(Date)  # تاريخ ميلاد رب الأسرة
    head_gender = Column(String(10), nullable=False)  # جنس رب الأسرة
    head_marital_status = Column(String(20))  # الحالة الاجتماعية
    phone_number = Column(String(20))  # رقم الهاتف
    identity_document = Column(String(50))  # وثيقة إثبات الهوية
    arrival_date = Column(Date, nullable=False)  # تاريخ الوصول للمخيم
    tent_number = Column(String(20))  # رقم الخيمة/المأوى
    shelter_type = Column(String(30))  # نوع المأوى
    family_size = Column(Integer, default=1)  # عدد أفراد الأسرة
    nationality = Column(String(50))  # الجنسية
    origin_country = Column(String(50))  # بلد المنشأ
    origin_city = Column(String(50))  # مدينة المنشأ
    special_needs = Column(Text)  # احتياجات خاصة
    medical_conditions = Column(Text)  # حالات طبية
    is_active = Column(Boolean, default=True)  # حالة الأسرة (نشطة/مؤرشفة)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    notes = Column(Text)  # ملاحظات

    # العلاقات
    individuals = relationship("Individual", back_populates="family", cascade="all, delete-orphan")
    # aid_distributions = relationship("AidDistribution", back_populates="family")  # سيتم تفعيلها لاحقاً

    def update_family_size(self):
        """تحديث عدد أفراد الأسرة تلقائياً"""
        self.family_size = len(self.individuals) + 1  # +1 لرب الأسرة

    def get_total_members(self) -> int:
        """الحصول على العدد الإجمالي لأفراد الأسرة"""
        return len(self.individuals) + 1  # +1 لرب الأسرة

    def get_children_count(self) -> int:
        """الحصول على عدد الأطفال (أقل من 18 سنة)"""
        from datetime import date
        children_count = 0
        current_date = date.today()

        for individual in self.individuals:
            if individual.birth_date:
                age = (current_date - individual.birth_date).days // 365
                if age < 18:
                    children_count += 1

        return children_count

    def get_adults_count(self) -> int:
        """الحصول على عدد البالغين"""
        return self.get_total_members() - self.get_children_count()

    def __repr__(self):
        return f"<Family(family_id='{self.family_id}', head_name='{self.head_of_family_name}')>"
