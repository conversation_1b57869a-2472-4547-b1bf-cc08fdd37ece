"""
نموذج الأفراد
Individual Model
"""
from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text, Date, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from ..config.database import Base

class Individual(Base):
    """نموذج الفرد"""
    __tablename__ = "individuals"
    
    id = Column(Integer, primary_key=True, index=True)
    family_id = Column(Integer, ForeignKey("families.id"), nullable=False)
    full_name = Column(String(100), nullable=False)  # الاسم الكامل
    birth_date = Column(Date)  # تاريخ الميلاد
    gender = Column(String(10), nullable=False)  # الجنس
    relationship_to_head = Column(String(30), nullable=False)  # صلة القرابة برب الأسرة
    identity_document = Column(String(50))  # وثيقة إثبات الهوية
    education_level = Column(String(30))  # المستوى التعليمي
    occupation = Column(String(50))  # المهنة
    marital_status = Column(String(20))  # الحالة الاجتماعية
    medical_conditions = Column(Text)  # حالات طبية
    special_needs = Column(Text)  # احتياجات خاصة
    is_active = Column(Boolean, default=True)  # حالة الفرد
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    notes = Column(Text)  # ملاحظات
    
    # العلاقات
    family = relationship("Family", back_populates="individuals")
    
    def get_age(self) -> int:
        """حساب العمر بالسنوات"""
        if not self.birth_date:
            return 0
        
        from datetime import date
        today = date.today()
        age = today.year - self.birth_date.year
        
        # تعديل العمر إذا لم يحن موعد عيد الميلاد بعد
        if today.month < self.birth_date.month or \
           (today.month == self.birth_date.month and today.day < self.birth_date.day):
            age -= 1
            
        return max(0, age)
    
    def is_child(self) -> bool:
        """التحقق من كون الفرد طفلاً (أقل من 18 سنة)"""
        return self.get_age() < 18
    
    def is_elderly(self) -> bool:
        """التحقق من كون الفرد مسناً (أكبر من 60 سنة)"""
        return self.get_age() > 60
    
    def get_age_group(self) -> str:
        """الحصول على الفئة العمرية"""
        age = self.get_age()
        
        if age < 5:
            return "طفل رضيع (0-4)"
        elif age < 12:
            return "طفل (5-11)"
        elif age < 18:
            return "مراهق (12-17)"
        elif age < 30:
            return "شاب (18-29)"
        elif age < 50:
            return "بالغ (30-49)"
        elif age < 65:
            return "متوسط العمر (50-64)"
        else:
            return "مسن (65+)"
    
    def __repr__(self):
        return f"<Individual(name='{self.full_name}', relationship='{self.relationship_to_head}')>"
