"""
نموذج المستخدمين
User Model
"""
from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text
from sqlalchemy.sql import func
from ..config.database import Base
import hashlib

class User(Base):
    """نموذج المستخدم"""
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(100), unique=True, index=True)
    full_name = Column(String(100), nullable=False)
    password_hash = Column(String(255), nullable=False)
    role = Column(String(20), nullable=False, default="viewer")
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_login = Column(DateTime(timezone=True))
    notes = Column(Text)

    def set_password(self, password: str):
        """تشفير وحفظ كلمة المرور"""
        # استخدام SHA256 مع salt بسيط (للاختبار فقط)
        salt = "camp_salt_2024"
        combined = password + salt
        self.password_hash = hashlib.sha256(combined.encode('utf-8')).hexdigest()

    def check_password(self, password: str) -> bool:
        """التحقق من كلمة المرور"""
        salt = "camp_salt_2024"
        combined = password + salt
        password_hash = hashlib.sha256(combined.encode('utf-8')).hexdigest()
        return password_hash == self.password_hash

    def has_permission(self, permission: str) -> bool:
        """التحقق من صلاحية المستخدم"""
        from ..config.settings import ROLE_PERMISSIONS
        user_permissions = ROLE_PERMISSIONS.get(self.role, [])
        return permission in user_permissions

    def get_role_display(self) -> str:
        """الحصول على اسم الدور باللغة العربية"""
        from ..config.settings import USER_ROLES
        return USER_ROLES.get(self.role, self.role)

    def __repr__(self):
        return f"<User(username='{self.username}', role='{self.role}')>"
