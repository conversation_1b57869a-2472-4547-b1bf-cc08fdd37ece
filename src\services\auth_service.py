"""
خدمة المصادقة والتحقق من الهوية
Authentication Service
"""
from sqlalchemy.orm import Session
from sqlalchemy import and_
from datetime import datetime
from typing import Optional, Tuple
from ..models.user import User
from ..config.database import get_db_session

class AuthService:
    """خدمة المصادقة"""

    def __init__(self):
        self.current_user: Optional[User] = None
        self.db: Session = get_db_session()

    def login(self, username: str, password: str) -> Tuple[bool, str, Optional[User]]:
        """
        تسجيل الدخول
        Returns: (success, message, user)
        """
        try:
            # البحث عن المستخدم
            user = self.db.query(User).filter(
                and_(
                    User.username == username,
                    User.is_active == True
                )
            ).first()

            if not user:
                return False, "اسم المستخدم غير موجود أو غير نشط", None

            # التحقق من كلمة المرور
            if not user.check_password(password):
                return False, "كلمة المرور غير صحيحة", None

            # تحديث آخر تسجيل دخول
            user.last_login = datetime.now()
            self.db.commit()

            # حفظ المستخدم الحالي
            self.current_user = user

            return True, f"مرحباً {user.full_name}", user

        except Exception as e:
            self.db.rollback()
            return False, f"خطأ في تسجيل الدخول: {str(e)}", None

    def logout(self):
        """تسجيل الخروج"""
        self.current_user = None

    def is_authenticated(self) -> bool:
        """التحقق من تسجيل الدخول"""
        return self.current_user is not None

    def has_permission(self, permission: str) -> bool:
        """التحقق من الصلاحية"""
        if not self.is_authenticated():
            return False
        return self.current_user.has_permission(permission)

    def get_current_user(self) -> Optional[User]:
        """الحصول على المستخدم الحالي"""
        return self.current_user

    def create_user(self, username: str, password: str, full_name: str,
                   email: str = None, role: str = "viewer") -> Tuple[bool, str]:
        """
        إنشاء مستخدم جديد
        """
        try:
            # التحقق من عدم وجود المستخدم
            existing_user = self.db.query(User).filter(
                User.username == username
            ).first()

            if existing_user:
                return False, "اسم المستخدم موجود بالفعل"

            # إنشاء المستخدم الجديد
            new_user = User(
                username=username,
                full_name=full_name,
                email=email,
                role=role
            )
            new_user.set_password(password)

            self.db.add(new_user)
            self.db.commit()

            return True, "تم إنشاء المستخدم بنجاح"

        except Exception as e:
            self.db.rollback()
            return False, f"خطأ في إنشاء المستخدم: {str(e)}"

    def change_password(self, old_password: str, new_password: str) -> Tuple[bool, str]:
        """تغيير كلمة المرور"""
        if not self.is_authenticated():
            return False, "يجب تسجيل الدخول أولاً"

        try:
            # التحقق من كلمة المرور القديمة
            if not self.current_user.check_password(old_password):
                return False, "كلمة المرور القديمة غير صحيحة"

            # تحديث كلمة المرور
            self.current_user.set_password(new_password)
            self.db.commit()

            return True, "تم تغيير كلمة المرور بنجاح"

        except Exception as e:
            self.db.rollback()
            return False, f"خطأ في تغيير كلمة المرور: {str(e)}"

    def get_all_users(self):
        """الحصول على جميع المستخدمين"""
        return self.db.query(User).all()

    def create_default_admin(self):
        """إنشاء مدير افتراضي"""
        try:
            # التحقق من وجود مدير
            admin_exists = self.db.query(User).filter(User.role == "admin").first()

            if not admin_exists:
                admin = User(
                    username="admin",
                    full_name="المدير الرئيسي",
                    email="<EMAIL>",
                    role="admin"
                )
                admin.set_password("admin123")  # كلمة مرور افتراضية

                self.db.add(admin)
                self.db.commit()

                print("تم إنشاء المدير الافتراضي:")
                print("اسم المستخدم: admin")
                print("كلمة المرور: admin123")
                print("يرجى تغيير كلمة المرور بعد أول تسجيل دخول")

        except Exception as e:
            self.db.rollback()
            print(f"خطأ في إنشاء المدير الافتراضي: {str(e)}")

    def has_permission(self, module: str, action: str) -> bool:
        """التحقق من صلاحية المستخدم للوحدة والعملية المحددة"""
        if not self.current_user:
            return False

        from ..config.settings import ROLE_PERMISSIONS
        role_permissions = ROLE_PERMISSIONS.get(self.current_user.role, {})
        module_permissions = role_permissions.get(module, [])
        return action in module_permissions

    def has_any_permission(self, module: str) -> bool:
        """التحقق من وجود أي صلاحية للوحدة"""
        if not self.current_user:
            return False

        from ..config.settings import ROLE_PERMISSIONS
        role_permissions = ROLE_PERMISSIONS.get(self.current_user.role, {})
        module_permissions = role_permissions.get(module, [])
        return len(module_permissions) > 0

    def get_user_permissions(self) -> dict:
        """الحصول على جميع صلاحيات المستخدم الحالي"""
        if not self.current_user:
            return {}

        from ..config.settings import ROLE_PERMISSIONS
        return ROLE_PERMISSIONS.get(self.current_user.role, {})

    def can_manage_users(self) -> bool:
        """التحقق من صلاحية إدارة المستخدمين"""
        return self.has_permission("users", "read")

    def can_create_user(self) -> bool:
        """التحقق من صلاحية إنشاء مستخدم"""
        return self.has_permission("users", "create")

    def can_delete_user(self) -> bool:
        """التحقق من صلاحية حذف مستخدم"""
        return self.has_permission("users", "delete")

    def __del__(self):
        """إغلاق اتصال قاعدة البيانات"""
        if hasattr(self, 'db'):
            self.db.close()
