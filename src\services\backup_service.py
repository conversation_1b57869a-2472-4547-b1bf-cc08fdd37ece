"""
خدمة النسخ الاحتياطي والاستعادة
Backup and Restore Service
"""
import os
import shutil
import sqlite3
import json
import zipfile
from datetime import datetime
from typing import List, Dict, Any, Tuple, Optional
from pathlib import Path

class BackupService:
    """خدمة النسخ الاحتياطي والاستعادة"""

    def __init__(self):
        self.backup_dir = "backups"
        self.db_path = "data/camp_management.db"
        self.config_dir = "data"

        # إنشاء مجلد النسخ الاحتياطي
        os.makedirs(self.backup_dir, exist_ok=True)

    def create_backup(self, backup_name: str = None, include_files: bool = True) -> Tuple[bool, str, Optional[str]]:
        """إنشاء نسخة احتياطية"""
        try:
            # تحديد اسم النسخة الاحتياطية
            if not backup_name:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_name = f"backup_{timestamp}"

            backup_filename = f"{backup_name}.zip"
            backup_path = os.path.join(self.backup_dir, backup_filename)

            # إنشاء ملف ZIP
            with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # نسخ قاعدة البيانات
                if os.path.exists(self.db_path):
                    zipf.write(self.db_path, "database/camp_management.db")

                # نسخ ملفات التكوين
                if os.path.exists(self.config_dir):
                    for root, dirs, files in os.walk(self.config_dir):
                        for file in files:
                            if file.endswith(('.json', '.txt', '.cfg')):
                                file_path = os.path.join(root, file)
                                arc_path = os.path.relpath(file_path, self.config_dir)
                                zipf.write(file_path, f"config/{arc_path}")

                # نسخ الملفات الإضافية إذا طُلب ذلك
                if include_files:
                    # نسخ قوالب Excel
                    templates_dir = "templates"
                    if os.path.exists(templates_dir):
                        for root, dirs, files in os.walk(templates_dir):
                            for file in files:
                                file_path = os.path.join(root, file)
                                arc_path = os.path.relpath(file_path, templates_dir)
                                zipf.write(file_path, f"templates/{arc_path}")

                    # نسخ ملفات Excel المصدرة
                    for file in os.listdir("."):
                        if file.endswith(('.xlsx', '.xls')):
                            zipf.write(file, f"exports/{file}")

                # إضافة معلومات النسخة الاحتياطية
                backup_info = {
                    "backup_name": backup_name,
                    "created_at": datetime.now().isoformat(),
                    "version": "1.0.0",
                    "database_included": os.path.exists(self.db_path),
                    "files_included": include_files,
                    "description": f"نسخة احتياطية تلقائية - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                }

                zipf.writestr("backup_info.json", json.dumps(backup_info, ensure_ascii=False, indent=2))

            # التحقق من حجم الملف
            file_size = os.path.getsize(backup_path)
            size_mb = file_size / (1024 * 1024)

            return True, f"تم إنشاء النسخة الاحتياطية بنجاح\nالملف: {backup_filename}\nالحجم: {size_mb:.2f} MB", backup_path

        except Exception as e:
            return False, f"فشل في إنشاء النسخة الاحتياطية: {str(e)}", None

    def restore_backup(self, backup_path: str, restore_database: bool = True,
                      restore_config: bool = True) -> Tuple[bool, str]:
        """استعادة نسخة احتياطية"""
        try:
            if not os.path.exists(backup_path):
                return False, "ملف النسخة الاحتياطية غير موجود"

            # إنشاء نسخة احتياطية من الحالة الحالية قبل الاستعادة
            current_backup_success, current_backup_msg, _ = self.create_backup("before_restore_" + datetime.now().strftime("%Y%m%d_%H%M%S"))

            if not current_backup_success:
                return False, f"فشل في إنشاء نسخة احتياطية من الحالة الحالية: {current_backup_msg}"

            # استخراج النسخة الاحتياطية
            with zipfile.ZipFile(backup_path, 'r') as zipf:
                # قراءة معلومات النسخة الاحتياطية
                backup_info = {}
                try:
                    backup_info_str = zipf.read("backup_info.json").decode('utf-8')
                    backup_info = json.loads(backup_info_str)
                except:
                    pass

                # استعادة قاعدة البيانات
                if restore_database and "database/camp_management.db" in zipf.namelist():
                    # إنشاء مجلد البيانات إذا لم يكن موجوداً
                    os.makedirs(os.path.dirname(self.db_path), exist_ok=True)

                    # استخراج قاعدة البيانات
                    with zipf.open("database/camp_management.db") as source, open(self.db_path, "wb") as target:
                        shutil.copyfileobj(source, target)

                # استعادة ملفات التكوين
                if restore_config:
                    for file_info in zipf.infolist():
                        if file_info.filename.startswith("config/"):
                            # تحديد المسار المحلي
                            local_path = os.path.join(self.config_dir, file_info.filename[7:])  # إزالة "config/"

                            # إنشاء المجلدات إذا لم تكن موجودة
                            os.makedirs(os.path.dirname(local_path), exist_ok=True)

                            # استخراج الملف
                            with zipf.open(file_info) as source, open(local_path, "wb") as target:
                                shutil.copyfileobj(source, target)

                # استعادة القوالب
                for file_info in zipf.infolist():
                    if file_info.filename.startswith("templates/"):
                        local_path = file_info.filename  # المسار كما هو
                        os.makedirs(os.path.dirname(local_path), exist_ok=True)

                        with zipf.open(file_info) as source, open(local_path, "wb") as target:
                            shutil.copyfileobj(source, target)

            restore_msg = f"تم استعادة النسخة الاحتياطية بنجاح"
            if backup_info:
                restore_msg += f"\nاسم النسخة: {backup_info.get('backup_name', 'غير محدد')}"
                restore_msg += f"\nتاريخ الإنشاء: {backup_info.get('created_at', 'غير محدد')}"

            return True, restore_msg

        except Exception as e:
            return False, f"فشل في استعادة النسخة الاحتياطية: {str(e)}"

    def list_backups(self) -> List[Dict[str, Any]]:
        """قائمة النسخ الاحتياطية المتاحة"""
        backups = []

        try:
            if not os.path.exists(self.backup_dir):
                return backups

            for filename in os.listdir(self.backup_dir):
                if filename.endswith('.zip'):
                    backup_path = os.path.join(self.backup_dir, filename)

                    # معلومات الملف
                    stat = os.stat(backup_path)
                    file_size = stat.st_size / (1024 * 1024)  # MB
                    modified_time = datetime.fromtimestamp(stat.st_mtime)

                    backup_info = {
                        'filename': filename,
                        'path': backup_path,
                        'size_mb': round(file_size, 2),
                        'created_at': modified_time.strftime('%Y-%m-%d %H:%M:%S'),
                        'name': filename.replace('.zip', ''),
                        'description': ''
                    }

                    # محاولة قراءة معلومات إضافية من داخل الملف
                    try:
                        with zipfile.ZipFile(backup_path, 'r') as zipf:
                            if "backup_info.json" in zipf.namelist():
                                info_str = zipf.read("backup_info.json").decode('utf-8')
                                info_data = json.loads(info_str)
                                backup_info.update({
                                    'name': info_data.get('backup_name', backup_info['name']),
                                    'description': info_data.get('description', ''),
                                    'version': info_data.get('version', ''),
                                    'database_included': info_data.get('database_included', False),
                                    'files_included': info_data.get('files_included', False)
                                })
                    except:
                        pass

                    backups.append(backup_info)

            # ترتيب حسب تاريخ الإنشاء (الأحدث أولاً)
            backups.sort(key=lambda x: x['created_at'], reverse=True)

        except Exception as e:
            print(f"خطأ في قراءة النسخ الاحتياطية: {str(e)}")

        return backups

    def delete_backup(self, backup_path: str) -> Tuple[bool, str]:
        """حذف نسخة احتياطية"""
        try:
            if not os.path.exists(backup_path):
                return False, "ملف النسخة الاحتياطية غير موجود"

            os.remove(backup_path)
            return True, "تم حذف النسخة الاحتياطية بنجاح"

        except Exception as e:
            return False, f"فشل في حذف النسخة الاحتياطية: {str(e)}"

    def verify_backup(self, backup_path: str) -> Tuple[bool, str, Dict[str, Any]]:
        """التحقق من صحة النسخة الاحتياطية"""
        try:
            if not os.path.exists(backup_path):
                return False, "ملف النسخة الاحتياطية غير موجود", {}

            verification_info = {
                'file_exists': True,
                'file_size': os.path.getsize(backup_path),
                'is_valid_zip': False,
                'contains_database': False,
                'contains_config': False,
                'backup_info': {}
            }

            # التحقق من أن الملف ZIP صالح
            with zipfile.ZipFile(backup_path, 'r') as zipf:
                verification_info['is_valid_zip'] = True

                # فحص محتويات الملف
                file_list = zipf.namelist()
                verification_info['contains_database'] = any(f.startswith('database/') for f in file_list)
                verification_info['contains_config'] = any(f.startswith('config/') for f in file_list)

                # قراءة معلومات النسخة الاحتياطية
                if "backup_info.json" in file_list:
                    info_str = zipf.read("backup_info.json").decode('utf-8')
                    verification_info['backup_info'] = json.loads(info_str)

                # اختبار استخراج ملف واحد للتأكد
                if file_list:
                    test_file = file_list[0]
                    zipf.read(test_file)

            status_msg = "النسخة الاحتياطية صالحة ويمكن استعادتها"
            return True, status_msg, verification_info

        except zipfile.BadZipFile:
            return False, "ملف النسخة الاحتياطية تالف أو غير صالح", verification_info
        except Exception as e:
            return False, f"خطأ في التحقق من النسخة الاحتياطية: {str(e)}", verification_info

    def cleanup_old_backups(self, keep_count: int = 10) -> Tuple[bool, str]:
        """تنظيف النسخ الاحتياطية القديمة"""
        try:
            backups = self.list_backups()

            if len(backups) <= keep_count:
                return True, f"عدد النسخ الاحتياطية ({len(backups)}) أقل من الحد المسموح ({keep_count})"

            # حذف النسخ الزائدة (الأقدم)
            backups_to_delete = backups[keep_count:]
            deleted_count = 0

            for backup in backups_to_delete:
                success, msg = self.delete_backup(backup['path'])
                if success:
                    deleted_count += 1

            return True, f"تم حذف {deleted_count} نسخة احتياطية قديمة"

        except Exception as e:
            return False, f"فشل في تنظيف النسخ الاحتياطية: {str(e)}"

    def export_backup_info(self, output_file: str = "backup_report.txt") -> Tuple[bool, str]:
        """تصدير تقرير النسخ الاحتياطية"""
        try:
            backups = self.list_backups()

            with open(output_file, 'w', encoding='utf-8') as f:
                f.write("تقرير النسخ الاحتياطية\n")
                f.write("=" * 50 + "\n\n")
                f.write(f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"عدد النسخ الاحتياطية: {len(backups)}\n\n")

                total_size = 0
                for i, backup in enumerate(backups, 1):
                    f.write(f"{i}. {backup['name']}\n")
                    f.write(f"   الملف: {backup['filename']}\n")
                    f.write(f"   الحجم: {backup['size_mb']} MB\n")
                    f.write(f"   تاريخ الإنشاء: {backup['created_at']}\n")
                    if backup['description']:
                        f.write(f"   الوصف: {backup['description']}\n")
                    f.write("\n")
                    total_size += backup['size_mb']

                f.write(f"إجمالي حجم النسخ الاحتياطية: {total_size:.2f} MB\n")

            return True, f"تم تصدير تقرير النسخ الاحتياطية إلى: {output_file}"

        except Exception as e:
            return False, f"فشل في تصدير التقرير: {str(e)}"

    def schedule_auto_backup(self, interval_hours: int = 24) -> bool:
        """جدولة النسخ الاحتياطي التلقائي"""
        # هذه الميزة تحتاج إلى تطوير إضافي مع مكتبة scheduling
        # يمكن تطويرها لاحقاً باستخدام APScheduler أو threading
        return False
