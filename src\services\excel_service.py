"""
خدمة التعامل مع ملفات Excel
Excel Service
"""
import pandas as pd
from openpyxl import Workbook, load_workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from typing import List, Dict, Any, Tuple, Optional
from datetime import datetime, date
import os
from ..models.family import Family
from ..models.individual import Individual
from ..config.settings import TEMPLATES_DIR

class ExcelService:
    """خدمة التعامل مع ملفات Excel"""
    
    def __init__(self):
        self.templates_dir = TEMPLATES_DIR
        
    def create_families_template(self, file_path: str) -> bool:
        """إنشاء قالب Excel لاستيراد الأسر"""
        try:
            wb = Workbook()
            ws = wb.active
            ws.title = "الأسر"
            
            # تعريف الأعمدة
            headers = [
                "اسم رب الأسرة", "تاريخ الميلاد (رب الأسرة)", "الجنس", "الحالة الاجتماعية",
                "رقم الهاتف", "وثيقة الهوية", "تاريخ الوصول", "رقم الخيمة", "نوع المأوى",
                "الجنسية", "بلد المنشأ", "مدينة المنشأ", "احتياجات خاصة", "حالات طبية", "ملاحظات"
            ]
            
            # إضافة العناوين
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col, value=header)
                cell.font = Font(bold=True, color="FFFFFF")
                cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                cell.alignment = Alignment(horizontal="center", vertical="center")
                cell.border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )
            
            # إضافة صف مثال
            example_row = [
                "أحمد محمد علي", "1980-01-15", "ذكر", "متزوج",
                "+970123456789", "123456789", "2024-01-01", "A-001", "خيمة",
                "فلسطيني", "فلسطين", "غزة", "", "", "مثال على البيانات"
            ]
            
            for col, value in enumerate(example_row, 1):
                cell = ws.cell(row=2, column=col, value=value)
                cell.fill = PatternFill(start_color="E7E6E6", end_color="E7E6E6", fill_type="solid")
            
            # تعديل عرض الأعمدة
            for col in range(1, len(headers) + 1):
                ws.column_dimensions[ws.cell(row=1, column=col).column_letter].width = 15
            
            # إضافة ورقة للأفراد
            ws_individuals = wb.create_sheet("الأفراد")
            individual_headers = [
                "رقم الأسرة", "الاسم الكامل", "تاريخ الميلاد", "الجنس", "صلة القرابة",
                "وثيقة الهوية", "المستوى التعليمي", "المهنة", "الحالة الاجتماعية",
                "حالات طبية", "احتياجات خاصة", "ملاحظات"
            ]
            
            for col, header in enumerate(individual_headers, 1):
                cell = ws_individuals.cell(row=1, column=col, value=header)
                cell.font = Font(bold=True, color="FFFFFF")
                cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                cell.alignment = Alignment(horizontal="center", vertical="center")
                ws_individuals.column_dimensions[cell.column_letter].width = 15
            
            # إضافة مثال للأفراد
            individual_example = [
                "FAM000001", "فاطمة أحمد محمد", "2005-03-20", "أنثى", "ابنة",
                "987654321", "ثانوي", "طالبة", "عزباء", "", "", ""
            ]
            
            for col, value in enumerate(individual_example, 1):
                cell = ws_individuals.cell(row=2, column=col, value=value)
                cell.fill = PatternFill(start_color="E7E6E6", end_color="E7E6E6", fill_type="solid")
            
            wb.save(file_path)
            return True
            
        except Exception as e:
            print(f"خطأ في إنشاء قالب Excel: {str(e)}")
            return False
    
    def export_families_to_excel(self, families: List[Family], file_path: str) -> bool:
        """تصدير الأسر إلى ملف Excel"""
        try:
            # إعداد البيانات للأسر
            families_data = []
            individuals_data = []
            
            for family in families:
                family_row = {
                    'رقم الأسرة': family.family_id,
                    'اسم رب الأسرة': family.head_of_family_name,
                    'تاريخ الميلاد': family.head_birth_date.strftime('%Y-%m-%d') if family.head_birth_date else '',
                    'الجنس': family.head_gender,
                    'الحالة الاجتماعية': family.head_marital_status or '',
                    'رقم الهاتف': family.phone_number or '',
                    'وثيقة الهوية': family.identity_document or '',
                    'تاريخ الوصول': family.arrival_date.strftime('%Y-%m-%d') if family.arrival_date else '',
                    'رقم الخيمة': family.tent_number or '',
                    'نوع المأوى': family.shelter_type or '',
                    'عدد الأفراد': family.get_total_members(),
                    'الجنسية': family.nationality or '',
                    'بلد المنشأ': family.origin_country or '',
                    'مدينة المنشأ': family.origin_city or '',
                    'احتياجات خاصة': family.special_needs or '',
                    'حالات طبية': family.medical_conditions or '',
                    'تاريخ التسجيل': family.created_at.strftime('%Y-%m-%d %H:%M') if family.created_at else '',
                    'ملاحظات': family.notes or ''
                }
                families_data.append(family_row)
                
                # إضافة أفراد الأسرة
                for individual in family.individuals:
                    individual_row = {
                        'رقم الأسرة': family.family_id,
                        'الاسم الكامل': individual.full_name,
                        'تاريخ الميلاد': individual.birth_date.strftime('%Y-%m-%d') if individual.birth_date else '',
                        'العمر': individual.get_age(),
                        'الجنس': individual.gender,
                        'صلة القرابة': individual.relationship_to_head,
                        'وثيقة الهوية': individual.identity_document or '',
                        'المستوى التعليمي': individual.education_level or '',
                        'المهنة': individual.occupation or '',
                        'الحالة الاجتماعية': individual.marital_status or '',
                        'حالات طبية': individual.medical_conditions or '',
                        'احتياجات خاصة': individual.special_needs or '',
                        'الفئة العمرية': individual.get_age_group(),
                        'ملاحظات': individual.notes or ''
                    }
                    individuals_data.append(individual_row)
            
            # إنشاء ملف Excel
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                # ورقة الأسر
                families_df = pd.DataFrame(families_data)
                families_df.to_excel(writer, sheet_name='الأسر', index=False)
                
                # ورقة الأفراد
                if individuals_data:
                    individuals_df = pd.DataFrame(individuals_data)
                    individuals_df.to_excel(writer, sheet_name='الأفراد', index=False)
                
                # تنسيق الملف
                workbook = writer.book
                for sheet_name in workbook.sheetnames:
                    worksheet = workbook[sheet_name]
                    
                    # تنسيق العناوين
                    for cell in worksheet[1]:
                        cell.font = Font(bold=True, color="FFFFFF")
                        cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                        cell.alignment = Alignment(horizontal="center", vertical="center")
                    
                    # تعديل عرض الأعمدة
                    for column in worksheet.columns:
                        max_length = 0
                        column_letter = column[0].column_letter
                        for cell in column:
                            try:
                                if len(str(cell.value)) > max_length:
                                    max_length = len(str(cell.value))
                            except:
                                pass
                        adjusted_width = min(max_length + 2, 30)
                        worksheet.column_dimensions[column_letter].width = adjusted_width
            
            return True
            
        except Exception as e:
            print(f"خطأ في تصدير البيانات: {str(e)}")
            return False
    
    def import_families_from_excel(self, file_path: str) -> Tuple[bool, str, List[Dict], List[Dict]]:
        """
        استيراد الأسر من ملف Excel
        Returns: (success, message, families_data, individuals_data)
        """
        try:
            if not os.path.exists(file_path):
                return False, "الملف غير موجود", [], []
            
            # قراءة ورقة الأسر
            families_df = pd.read_excel(file_path, sheet_name='الأسر')
            families_data = []
            
            for _, row in families_df.iterrows():
                if pd.isna(row.get('اسم رب الأسرة')):
                    continue
                    
                family_data = {
                    'head_of_family_name': str(row.get('اسم رب الأسرة', '')).strip(),
                    'head_birth_date': self._parse_date(row.get('تاريخ الميلاد (رب الأسرة)')),
                    'head_gender': str(row.get('الجنس', '')).strip(),
                    'head_marital_status': str(row.get('الحالة الاجتماعية', '')).strip(),
                    'phone_number': str(row.get('رقم الهاتف', '')).strip(),
                    'identity_document': str(row.get('وثيقة الهوية', '')).strip(),
                    'arrival_date': self._parse_date(row.get('تاريخ الوصول')),
                    'tent_number': str(row.get('رقم الخيمة', '')).strip(),
                    'shelter_type': str(row.get('نوع المأوى', '')).strip(),
                    'nationality': str(row.get('الجنسية', '')).strip(),
                    'origin_country': str(row.get('بلد المنشأ', '')).strip(),
                    'origin_city': str(row.get('مدينة المنشأ', '')).strip(),
                    'special_needs': str(row.get('احتياجات خاصة', '')).strip(),
                    'medical_conditions': str(row.get('حالات طبية', '')).strip(),
                    'notes': str(row.get('ملاحظات', '')).strip()
                }
                families_data.append(family_data)
            
            # قراءة ورقة الأفراد (إذا وجدت)
            individuals_data = []
            try:
                individuals_df = pd.read_excel(file_path, sheet_name='الأفراد')
                for _, row in individuals_df.iterrows():
                    if pd.isna(row.get('الاسم الكامل')):
                        continue
                        
                    individual_data = {
                        'family_id': str(row.get('رقم الأسرة', '')).strip(),
                        'full_name': str(row.get('الاسم الكامل', '')).strip(),
                        'birth_date': self._parse_date(row.get('تاريخ الميلاد')),
                        'gender': str(row.get('الجنس', '')).strip(),
                        'relationship_to_head': str(row.get('صلة القرابة', '')).strip(),
                        'identity_document': str(row.get('وثيقة الهوية', '')).strip(),
                        'education_level': str(row.get('المستوى التعليمي', '')).strip(),
                        'occupation': str(row.get('المهنة', '')).strip(),
                        'marital_status': str(row.get('الحالة الاجتماعية', '')).strip(),
                        'medical_conditions': str(row.get('حالات طبية', '')).strip(),
                        'special_needs': str(row.get('احتياجات خاصة', '')).strip(),
                        'notes': str(row.get('ملاحظات', '')).strip()
                    }
                    individuals_data.append(individual_data)
            except:
                pass  # ورقة الأفراد غير موجودة
            
            return True, f"تم قراءة {len(families_data)} أسرة و {len(individuals_data)} فرد", families_data, individuals_data
            
        except Exception as e:
            return False, f"خطأ في قراءة الملف: {str(e)}", [], []
    
    def _parse_date(self, date_value) -> Optional[date]:
        """تحويل التاريخ من Excel إلى كائن date"""
        if pd.isna(date_value):
            return None
            
        try:
            if isinstance(date_value, datetime):
                return date_value.date()
            elif isinstance(date_value, date):
                return date_value
            elif isinstance(date_value, str):
                # محاولة تحويل النص إلى تاريخ
                for fmt in ['%Y-%m-%d', '%d/%m/%Y', '%d-%m-%Y']:
                    try:
                        return datetime.strptime(date_value, fmt).date()
                    except:
                        continue
        except:
            pass
            
        return None
