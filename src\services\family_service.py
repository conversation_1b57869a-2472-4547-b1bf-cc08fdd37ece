"""
خدمة إدارة الأسر
Family Management Service
"""
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, desc, asc
from typing import List, Optional, Tuple, Dict, Any
from datetime import datetime, date
from ..models.family import Family
from ..models.individual import Individual
from ..config.database import get_db_session

class FamilyService:
    """خدمة إدارة الأسر"""

    def __init__(self):
        self.db: Session = get_db_session()

    def generate_family_id(self) -> str:
        """توليد رقم تعريف فريد للأسرة"""
        # الحصول على آخر رقم أسرة
        last_family = self.db.query(Family).order_by(desc(Family.id)).first()

        if last_family and last_family.family_id:
            try:
                # استخراج الرقم من آخر family_id
                last_number = int(last_family.family_id.replace("FAM", ""))
                new_number = last_number + 1
            except:
                new_number = 1
        else:
            new_number = 1

        return f"FAM{new_number:06d}"  # مثال: FAM000001

    def create_family(self, family_data: Dict[str, Any]) -> Tuple[bool, str, Optional[Family]]:
        """
        إنشاء أسرة جديدة
        """
        try:
            # توليد رقم تعريف الأسرة
            family_id = self.generate_family_id()

            # إنشاء الأسرة
            family = Family(
                family_id=family_id,
                head_of_family_name=family_data.get('head_of_family_name'),
                head_birth_date=family_data.get('head_birth_date'),
                head_gender=family_data.get('head_gender'),
                head_marital_status=family_data.get('head_marital_status'),
                phone_number=family_data.get('phone_number'),
                identity_document=family_data.get('identity_document'),
                arrival_date=family_data.get('arrival_date', date.today()),
                tent_number=family_data.get('tent_number'),
                shelter_type=family_data.get('shelter_type'),
                nationality=family_data.get('nationality'),
                origin_country=family_data.get('origin_country'),
                origin_city=family_data.get('origin_city'),
                special_needs=family_data.get('special_needs'),
                medical_conditions=family_data.get('medical_conditions'),
                notes=family_data.get('notes')
            )

            self.db.add(family)
            self.db.flush()  # للحصول على ID الأسرة

            # إضافة أفراد الأسرة
            individuals_data = family_data.get('individuals', [])
            if individuals_data:
                for individual_data in individuals_data:
                    individual = Individual(
                        family_id=family.id,
                        full_name=individual_data.get('full_name'),
                        birth_date=individual_data.get('birth_date'),
                        gender=individual_data.get('gender'),
                        relationship_to_head=individual_data.get('relationship_to_head'),
                        identity_document=individual_data.get('identity_document'),
                        education_level=individual_data.get('education_level'),
                        occupation=individual_data.get('occupation'),
                        marital_status=individual_data.get('marital_status'),
                        medical_conditions=individual_data.get('medical_conditions'),
                        special_needs=individual_data.get('special_needs'),
                        notes=individual_data.get('notes')
                    )
                    self.db.add(individual)

            # تحديث عدد أفراد الأسرة
            family.update_family_size()

            self.db.commit()

            return True, f"تم إنشاء الأسرة بنجاح - رقم التعريف: {family_id}", family

        except Exception as e:
            self.db.rollback()
            return False, f"خطأ في إنشاء الأسرة: {str(e)}", None

    def get_family_by_id(self, family_id: str) -> Optional[Family]:
        """الحصول على أسرة بواسطة رقم التعريف"""
        return self.db.query(Family).options(
            joinedload(Family.individuals)
        ).filter(Family.family_id == family_id).first()

    def get_all_families(self, include_inactive: bool = False) -> List[Family]:
        """الحصول على جميع الأسر"""
        query = self.db.query(Family).options(joinedload(Family.individuals))

        if not include_inactive:
            query = query.filter(Family.is_active == True)

        return query.order_by(Family.created_at.desc()).all()

    def search_families(self, search_term: str, search_fields: List[str] = None) -> List[Family]:
        """البحث في الأسر"""
        if not search_fields:
            search_fields = ['head_of_family_name', 'family_id', 'phone_number', 'tent_number']

        conditions = []
        for field in search_fields:
            if hasattr(Family, field):
                conditions.append(getattr(Family, field).ilike(f"%{search_term}%"))

        if conditions:
            return self.db.query(Family).options(
                joinedload(Family.individuals)
            ).filter(
                and_(Family.is_active == True, or_(*conditions))
            ).all()

        return []

    def update_family(self, family_id: str, family_data: Dict[str, Any]) -> Tuple[bool, str]:
        """تحديث بيانات أسرة"""
        try:
            family = self.get_family_by_id(family_id)
            if not family:
                return False, "الأسرة غير موجودة"

            # تحديث البيانات
            for key, value in family_data.items():
                if hasattr(family, key) and value is not None:
                    setattr(family, key, value)

            family.updated_at = datetime.now()
            self.db.commit()

            return True, "تم تحديث بيانات الأسرة بنجاح"

        except Exception as e:
            self.db.rollback()
            return False, f"خطأ في تحديث الأسرة: {str(e)}"

    def archive_family(self, family_id: str) -> Tuple[bool, str]:
        """أرشفة أسرة (حذف منطقي)"""
        try:
            family = self.get_family_by_id(family_id)
            if not family:
                return False, "الأسرة غير موجودة"

            family.is_active = False
            family.updated_at = datetime.now()
            self.db.commit()

            return True, "تم أرشفة الأسرة بنجاح"

        except Exception as e:
            self.db.rollback()
            return False, f"خطأ في أرشفة الأسرة: {str(e)}"

    def get_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الأسر"""
        try:
            total_families = self.db.query(Family).filter(Family.is_active == True).count()
            total_individuals = self.db.query(Individual).join(Family).filter(
                Family.is_active == True
            ).count()

            # إحصائيات إضافية
            children_count = self.db.query(Individual).join(Family).filter(
                and_(
                    Family.is_active == True,
                    Individual.birth_date.isnot(None)
                )
            ).all()

            children = sum(1 for ind in children_count if ind.is_child())
            adults = total_individuals - children

            return {
                'total_families': total_families,
                'total_individuals': total_individuals + total_families,  # +رؤساء الأسر
                'children_count': children,
                'adults_count': adults + total_families,  # +رؤساء الأسر
                'average_family_size': round((total_individuals + total_families) / max(total_families, 1), 2)
            }

        except Exception as e:
            return {
                'total_families': 0,
                'total_individuals': 0,
                'children_count': 0,
                'adults_count': 0,
                'average_family_size': 0,
                'error': str(e)
            }

    def __del__(self):
        """إغلاق اتصال قاعدة البيانات"""
        if hasattr(self, 'db'):
            self.db.close()
