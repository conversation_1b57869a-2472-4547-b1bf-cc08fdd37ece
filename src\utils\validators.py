"""
دوال التحقق من صحة البيانات
Data Validation Functions
"""
import re
from datetime import datetime, date
from typing import Optional, Tu<PERSON>

def validate_phone_number(phone: str) -> Tuple[bool, str]:
    """
    التحقق من صحة رقم الهاتف
    """
    if not phone:
        return True, ""  # رقم الهاتف اختياري
    
    # إزالة المسافات والرموز الإضافية
    clean_phone = re.sub(r'[^\d+]', '', phone)
    
    # التحقق من الطول والتنسيق
    if len(clean_phone) < 8:
        return False, "رقم الهاتف قصير جداً"
    
    if len(clean_phone) > 15:
        return False, "رقم الهاتف طويل جداً"
    
    # التحقق من التنسيق
    phone_pattern = r'^(\+?[1-9]\d{1,14})$'
    if not re.match(phone_pattern, clean_phone):
        return False, "تنسيق رقم الهاتف غير صحيح"
    
    return True, ""

def validate_identity_document(doc_id: str) -> <PERSON><PERSON>[bool, str]:
    """
    التحقق من صحة وثيقة الهوية
    """
    if not doc_id:
        return True, ""  # وثيقة الهوية اختيارية
    
    # إزالة المسافات
    clean_id = doc_id.strip()
    
    if len(clean_id) < 5:
        return False, "رقم الوثيقة قصير جداً"
    
    if len(clean_id) > 20:
        return False, "رقم الوثيقة طويل جداً"
    
    return True, ""

def validate_name(name: str) -> Tuple[bool, str]:
    """
    التحقق من صحة الاسم
    """
    if not name or not name.strip():
        return False, "الاسم مطلوب"
    
    clean_name = name.strip()
    
    if len(clean_name) < 2:
        return False, "الاسم قصير جداً"
    
    if len(clean_name) > 100:
        return False, "الاسم طويل جداً"
    
    # التحقق من وجود أحرف صالحة فقط
    name_pattern = r'^[a-zA-Zأ-ي\s\-\.]+$'
    if not re.match(name_pattern, clean_name):
        return False, "الاسم يحتوي على أحرف غير صالحة"
    
    return True, ""

def validate_date(date_value, field_name: str = "التاريخ") -> Tuple[bool, str]:
    """
    التحقق من صحة التاريخ
    """
    if not date_value:
        return True, ""  # التاريخ اختياري في معظم الحالات
    
    try:
        if isinstance(date_value, str):
            # محاولة تحويل النص إلى تاريخ
            parsed_date = datetime.strptime(date_value, '%Y-%m-%d').date()
        elif isinstance(date_value, datetime):
            parsed_date = date_value.date()
        elif isinstance(date_value, date):
            parsed_date = date_value
        else:
            return False, f"{field_name} غير صالح"
        
        # التحقق من أن التاريخ ليس في المستقبل (للمواليد)
        if field_name == "تاريخ الميلاد" and parsed_date > date.today():
            return False, "تاريخ الميلاد لا يمكن أن يكون في المستقبل"
        
        # التحقق من أن التاريخ ليس قديماً جداً (أكثر من 150 سنة)
        if field_name == "تاريخ الميلاد":
            min_date = date.today().replace(year=date.today().year - 150)
            if parsed_date < min_date:
                return False, "تاريخ الميلاد قديم جداً"
        
        return True, ""
        
    except ValueError:
        return False, f"{field_name} غير صالح"

def validate_tent_number(tent_number: str) -> Tuple[bool, str]:
    """
    التحقق من صحة رقم الخيمة
    """
    if not tent_number:
        return True, ""  # رقم الخيمة اختياري
    
    clean_tent = tent_number.strip()
    
    if len(clean_tent) > 20:
        return False, "رقم الخيمة طويل جداً"
    
    # التحقق من التنسيق (أحرف وأرقام ورموز بسيطة)
    tent_pattern = r'^[a-zA-Z0-9\-_/]+$'
    if not re.match(tent_pattern, clean_tent):
        return False, "رقم الخيمة يحتوي على أحرف غير صالحة"
    
    return True, ""

def validate_family_data(family_data: dict) -> Tuple[bool, list]:
    """
    التحقق من صحة بيانات الأسرة
    """
    errors = []
    
    # التحقق من اسم رب الأسرة
    valid, error = validate_name(family_data.get('head_of_family_name', ''))
    if not valid:
        errors.append(f"اسم رب الأسرة: {error}")
    
    # التحقق من تاريخ الميلاد
    valid, error = validate_date(family_data.get('head_birth_date'), "تاريخ الميلاد")
    if not valid:
        errors.append(f"تاريخ الميلاد: {error}")
    
    # التحقق من رقم الهاتف
    valid, error = validate_phone_number(family_data.get('phone_number', ''))
    if not valid:
        errors.append(f"رقم الهاتف: {error}")
    
    # التحقق من وثيقة الهوية
    valid, error = validate_identity_document(family_data.get('identity_document', ''))
    if not valid:
        errors.append(f"وثيقة الهوية: {error}")
    
    # التحقق من تاريخ الوصول
    valid, error = validate_date(family_data.get('arrival_date'), "تاريخ الوصول")
    if not valid:
        errors.append(f"تاريخ الوصول: {error}")
    
    # التحقق من رقم الخيمة
    valid, error = validate_tent_number(family_data.get('tent_number', ''))
    if not valid:
        errors.append(f"رقم الخيمة: {error}")
    
    # التحقق من الجنس
    gender = family_data.get('head_gender', '')
    if gender and gender not in ['ذكر', 'أنثى']:
        errors.append("الجنس يجب أن يكون 'ذكر' أو 'أنثى'")
    
    return len(errors) == 0, errors

def validate_individual_data(individual_data: dict) -> Tuple[bool, list]:
    """
    التحقق من صحة بيانات الفرد
    """
    errors = []
    
    # التحقق من الاسم
    valid, error = validate_name(individual_data.get('full_name', ''))
    if not valid:
        errors.append(f"الاسم: {error}")
    
    # التحقق من تاريخ الميلاد
    valid, error = validate_date(individual_data.get('birth_date'), "تاريخ الميلاد")
    if not valid:
        errors.append(f"تاريخ الميلاد: {error}")
    
    # التحقق من الجنس
    gender = individual_data.get('gender', '')
    if gender and gender not in ['ذكر', 'أنثى']:
        errors.append("الجنس يجب أن يكون 'ذكر' أو 'أنثى'")
    
    # التحقق من صلة القرابة
    relationship = individual_data.get('relationship_to_head', '')
    if not relationship or not relationship.strip():
        errors.append("صلة القرابة مطلوبة")
    
    # التحقق من وثيقة الهوية
    valid, error = validate_identity_document(individual_data.get('identity_document', ''))
    if not valid:
        errors.append(f"وثيقة الهوية: {error}")
    
    return len(errors) == 0, errors

def sanitize_text(text: str) -> str:
    """
    تنظيف النص من الأحرف الضارة
    """
    if not text:
        return ""
    
    # إزالة المسافات الزائدة
    clean_text = text.strip()
    
    # إزالة الأحرف الخاصة الضارة
    clean_text = re.sub(r'[<>"\']', '', clean_text)
    
    # تحديد الطول الأقصى
    if len(clean_text) > 1000:
        clean_text = clean_text[:1000]
    
    return clean_text

def format_phone_number(phone: str) -> str:
    """
    تنسيق رقم الهاتف
    """
    if not phone:
        return ""
    
    # إزالة جميع الأحرف غير الرقمية عدا +
    clean_phone = re.sub(r'[^\d+]', '', phone)
    
    return clean_phone
