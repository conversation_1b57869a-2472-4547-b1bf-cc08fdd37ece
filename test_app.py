"""
اختبار بسيط للتطبيق
Simple Application Test
"""
import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """اختبار استيراد الوحدات الأساسية"""
    print("اختبار استيراد الوحدات...")
    
    try:
        # اختبار PyQt5
        from PyQt5.QtWidgets import QApplication
        print("✓ PyQt5 متوفر")
    except ImportError as e:
        print(f"✗ PyQt5 غير متوفر: {e}")
        return False
    
    try:
        # اختبار SQLAlchemy
        import sqlalchemy
        print("✓ SQLAlchemy متوفر")
    except ImportError as e:
        print(f"✗ SQLAlchemy غير متوفر: {e}")
        return False
    
    try:
        # اختبار bcrypt
        import bcrypt
        print("✓ bcrypt متوفر")
    except ImportError as e:
        print(f"✗ bcrypt غير متوفر: {e}")
        return False
    
    try:
        # اختبار openpyxl
        import openpyxl
        print("✓ openpyxl متوفر")
    except ImportError as e:
        print(f"✗ openpyxl غير متوفر: {e}")
        return False
    
    try:
        # اختبار matplotlib (اختياري)
        import matplotlib
        print("✓ matplotlib متوفر")
    except ImportError as e:
        print(f"⚠ matplotlib غير متوفر (اختياري): {e}")
    
    return True

def test_database():
    """اختبار قاعدة البيانات"""
    print("\nاختبار قاعدة البيانات...")
    
    try:
        from src.config.database import init_database, create_tables
        from src.config.settings import create_directories
        
        # إنشاء المجلدات
        create_directories()
        print("✓ تم إنشاء المجلدات")
        
        # تهيئة قاعدة البيانات
        init_database()
        print("✓ تم تهيئة قاعدة البيانات")
        
        # إنشاء الجداول
        create_tables()
        print("✓ تم إنشاء الجداول")
        
        return True
        
    except Exception as e:
        print(f"✗ خطأ في قاعدة البيانات: {e}")
        return False

def test_auth_service():
    """اختبار خدمة المصادقة"""
    print("\nاختبار خدمة المصادقة...")
    
    try:
        from src.services.auth_service import AuthService
        
        auth = AuthService()
        
        # إنشاء المدير الافتراضي
        auth.create_default_admin()
        print("✓ تم إنشاء المدير الافتراضي")
        
        # اختبار تسجيل الدخول
        success, message, user = auth.login("admin", "admin123")
        if success:
            print("✓ تسجيل الدخول نجح")
            print(f"  المستخدم: {user.full_name}")
            print(f"  الدور: {user.get_role_display()}")
        else:
            print(f"✗ فشل تسجيل الدخول: {message}")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ خطأ في خدمة المصادقة: {e}")
        return False

def test_family_service():
    """اختبار خدمة إدارة الأسر"""
    print("\nاختبار خدمة إدارة الأسر...")
    
    try:
        from src.services.family_service import FamilyService
        from datetime import date
        
        family_service = FamilyService()
        
        # إنشاء أسرة تجريبية
        family_data = {
            'head_of_family_name': 'أحمد محمد علي',
            'head_birth_date': date(1980, 1, 15),
            'head_gender': 'ذكر',
            'head_marital_status': 'متزوج',
            'phone_number': '+970123456789',
            'identity_document': '123456789',
            'arrival_date': date.today(),
            'tent_number': 'A-001',
            'shelter_type': 'خيمة',
            'nationality': 'فلسطيني',
            'origin_country': 'فلسطين',
            'origin_city': 'غزة'
        }
        
        success, message, family = family_service.create_family(family_data)
        if success:
            print("✓ تم إنشاء أسرة تجريبية")
            print(f"  رقم الأسرة: {family.family_id}")
            print(f"  اسم رب الأسرة: {family.head_of_family_name}")
        else:
            print(f"✗ فشل في إنشاء الأسرة: {message}")
            return False
        
        # اختبار الإحصائيات
        stats = family_service.get_statistics()
        print(f"✓ الإحصائيات: {stats['total_families']} أسرة، {stats['total_individuals']} فرد")
        
        return True
        
    except Exception as e:
        print(f"✗ خطأ في خدمة الأسر: {e}")
        return False

def test_excel_service():
    """اختبار خدمة Excel"""
    print("\nاختبار خدمة Excel...")
    
    try:
        from src.services.excel_service import ExcelService
        import tempfile
        import os
        
        excel_service = ExcelService()
        
        # إنشاء ملف مؤقت
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp:
            temp_file = tmp.name
        
        # إنشاء قالب
        success = excel_service.create_families_template(temp_file)
        if success:
            print("✓ تم إنشاء قالب Excel")
        else:
            print("✗ فشل في إنشاء قالب Excel")
            return False
        
        # تنظيف الملف المؤقت
        os.unlink(temp_file)
        
        return True
        
    except Exception as e:
        print(f"✗ خطأ في خدمة Excel: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("=== اختبار نظام إدارة المخيم ===")
    print()
    
    tests = [
        ("استيراد الوحدات", test_imports),
        ("قاعدة البيانات", test_database),
        ("خدمة المصادقة", test_auth_service),
        ("خدمة الأسر", test_family_service),
        ("خدمة Excel", test_excel_service)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"--- {test_name} ---")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} نجح")
            else:
                print(f"✗ {test_name} فشل")
        except Exception as e:
            print(f"✗ {test_name} فشل: {e}")
        print()
    
    print("=== نتائج الاختبار ===")
    print(f"نجح: {passed}/{total}")
    print(f"فشل: {total - passed}/{total}")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! التطبيق جاهز للتشغيل.")
        return 0
    else:
        print("⚠ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
