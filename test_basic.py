"""
اختبار أساسي للتطبيق بدون المكتبات الخارجية
Basic Application Test without External Libraries
"""
import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_basic_imports():
    """اختبار استيراد الوحدات الأساسية"""
    print("اختبار استيراد الوحدات الأساسية...")
    
    try:
        # اختبار SQLAlchemy
        import sqlalchemy
        print("✓ SQLAlchemy متوفر")
    except ImportError as e:
        print(f"✗ SQLAlchemy غير متوفر: {e}")
        return False
    
    try:
        # اختبار الوحدات المحلية
        from src.config import settings
        print("✓ إعدادات التطبيق")
        
        from src.config import database
        print("✓ إعدادات قاعدة البيانات")
        
        from src.models import family, individual, user
        print("✓ نماذج قاعدة البيانات")
        
        return True
        
    except Exception as e:
        print(f"✗ خطأ في استيراد الوحدات: {e}")
        return False

def test_database_basic():
    """اختبار قاعدة البيانات الأساسي"""
    print("\nاختبار قاعدة البيانات...")
    
    try:
        from src.config.database import init_database, Base, engine
        from src.config.settings import create_directories
        from src.models.family import Family
        from src.models.individual import Individual
        from src.models.user import User
        
        # إنشاء المجلدات
        create_directories()
        print("✓ تم إنشاء المجلدات")
        
        # تهيئة قاعدة البيانات
        init_database()
        print("✓ تم تهيئة قاعدة البيانات")
        
        # إنشاء الجداول (بدون aid_distribution)
        Base.metadata.create_all(bind=engine)
        print("✓ تم إنشاء الجداول")
        
        return True
        
    except Exception as e:
        print(f"✗ خطأ في قاعدة البيانات: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_family_model():
    """اختبار نموذج الأسرة"""
    print("\nاختبار نموذج الأسرة...")
    
    try:
        from src.models.family import Family
        from src.models.individual import Individual
        from src.config.database import get_db_session
        from datetime import date
        
        # إنشاء جلسة قاعدة البيانات
        db = get_db_session()
        
        # إنشاء أسرة تجريبية
        family = Family(
            family_id="FAM000001",
            head_of_family_name="أحمد محمد علي",
            head_birth_date=date(1980, 1, 15),
            head_gender="ذكر",
            head_marital_status="متزوج",
            phone_number="+970123456789",
            arrival_date=date.today(),
            tent_number="A-001",
            nationality="فلسطيني"
        )
        
        # إضافة فرد للأسرة
        individual = Individual(
            full_name="فاطمة أحمد محمد",
            birth_date=date(2005, 3, 20),
            gender="أنثى",
            relationship_to_head="ابنة"
        )
        
        family.individuals.append(individual)
        family.update_family_size()
        
        # حفظ في قاعدة البيانات
        db.add(family)
        db.commit()
        
        print("✓ تم إنشاء أسرة تجريبية")
        print(f"  رقم الأسرة: {family.family_id}")
        print(f"  اسم رب الأسرة: {family.head_of_family_name}")
        print(f"  عدد الأفراد: {family.get_total_members()}")
        print(f"  عدد الأطفال: {family.get_children_count()}")
        
        # اختبار البحث
        found_family = db.query(Family).filter(Family.family_id == "FAM000001").first()
        if found_family:
            print("✓ تم العثور على الأسرة في قاعدة البيانات")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"✗ خطأ في نموذج الأسرة: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_settings():
    """اختبار الإعدادات"""
    print("\nاختبار الإعدادات...")
    
    try:
        from src.config.settings import (
            BASE_DIR, DATABASE_DIR, RESOURCES_DIR,
            USER_ROLES, ROLE_PERMISSIONS, create_directories
        )
        
        print(f"✓ مجلد المشروع: {BASE_DIR}")
        print(f"✓ مجلد قاعدة البيانات: {DATABASE_DIR}")
        print(f"✓ مجلد الموارد: {RESOURCES_DIR}")
        print(f"✓ أدوار المستخدمين: {len(USER_ROLES)} دور")
        print(f"✓ صلاحيات الأدوار: {len(ROLE_PERMISSIONS)} دور")
        
        # إنشاء المجلدات
        create_directories()
        print("✓ تم إنشاء المجلدات المطلوبة")
        
        return True
        
    except Exception as e:
        print(f"✗ خطأ في الإعدادات: {e}")
        return False

def test_validators():
    """اختبار دوال التحقق"""
    print("\nاختبار دوال التحقق...")
    
    try:
        from src.utils.validators import (
            validate_name, validate_phone_number, 
            validate_date, validate_family_data
        )
        
        # اختبار التحقق من الاسم
        valid, error = validate_name("أحمد محمد علي")
        if valid:
            print("✓ التحقق من الاسم")
        else:
            print(f"✗ فشل التحقق من الاسم: {error}")
        
        # اختبار التحقق من رقم الهاتف
        valid, error = validate_phone_number("+970123456789")
        if valid:
            print("✓ التحقق من رقم الهاتف")
        else:
            print(f"✗ فشل التحقق من رقم الهاتف: {error}")
        
        # اختبار التحقق من التاريخ
        from datetime import date
        valid, error = validate_date(date.today(), "تاريخ الوصول")
        if valid:
            print("✓ التحقق من التاريخ")
        else:
            print(f"✗ فشل التحقق من التاريخ: {error}")
        
        # اختبار التحقق من بيانات الأسرة
        family_data = {
            'head_of_family_name': 'أحمد محمد علي',
            'head_gender': 'ذكر',
            'phone_number': '+970123456789',
            'arrival_date': date.today()
        }
        
        valid, errors = validate_family_data(family_data)
        if valid:
            print("✓ التحقق من بيانات الأسرة")
        else:
            print(f"✗ فشل التحقق من بيانات الأسرة: {errors}")
        
        return True
        
    except Exception as e:
        print(f"✗ خطأ في دوال التحقق: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("=== اختبار أساسي لنظام إدارة المخيم ===")
    print()
    
    tests = [
        ("استيراد الوحدات الأساسية", test_basic_imports),
        ("قاعدة البيانات الأساسية", test_database_basic),
        ("نموذج الأسرة", test_family_model),
        ("الإعدادات", test_settings),
        ("دوال التحقق", test_validators)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"--- {test_name} ---")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} نجح")
            else:
                print(f"✗ {test_name} فشل")
        except Exception as e:
            print(f"✗ {test_name} فشل: {e}")
        print()
    
    print("=== نتائج الاختبار ===")
    print(f"نجح: {passed}/{total}")
    print(f"فشل: {total - passed}/{total}")
    
    if passed == total:
        print("🎉 جميع الاختبارات الأساسية نجحت!")
        print("📝 ملاحظة: لتشغيل التطبيق كاملاً، يجب تثبيت المكتبات التالية:")
        print("   - PyQt5 (للواجهة الرسومية)")
        print("   - bcrypt (لتشفير كلمات المرور)")
        print("   - openpyxl (لملفات Excel)")
        print("   - matplotlib (للرسوم البيانية - اختياري)")
        return 0
    else:
        print("⚠ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
