"""
اختبار شامل لنظام إدارة المخيم
Complete Camp Management System Test
"""
import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_pyqt5():
    """اختبار PyQt5"""
    print("=== اختبار PyQt5 ===")
    
    try:
        from PyQt5.QtWidgets import QApplication
        print("✓ PyQt5 متوفر ويعمل")
        return True
    except ImportError as e:
        print(f"✗ PyQt5 غير متوفر: {e}")
        return False

def test_basic_modules():
    """اختبار الوحدات الأساسية"""
    print("\n=== اختبار الوحدات الأساسية ===")
    
    modules_to_test = [
        ("SQLAlchemy", "sqlalchemy"),
        ("pandas", "pandas"),
        ("openpyxl", "openpyxl"),
        ("python-dateutil", "dateutil"),
    ]
    
    available_modules = []
    
    for module_name, import_name in modules_to_test:
        try:
            __import__(import_name)
            print(f"✓ {module_name} متوفر")
            available_modules.append(module_name)
        except ImportError:
            print(f"✗ {module_name} غير متوفر")
    
    return available_modules

def test_project_structure():
    """اختبار هيكل المشروع"""
    print("\n=== اختبار هيكل المشروع ===")
    
    try:
        # اختبار استيراد الإعدادات
        from src.config.settings import create_directories, BASE_DIR
        print("✓ إعدادات التطبيق")
        
        # إنشاء المجلدات
        create_directories()
        print("✓ تم إنشاء المجلدات")
        
        # اختبار قاعدة البيانات
        from src.config.database import init_database, Base, engine
        init_database()
        print("✓ تم تهيئة قاعدة البيانات")
        
        # اختبار النماذج
        from src.models.user import User
        from src.models.family import Family
        from src.models.individual import Individual
        print("✓ تم استيراد النماذج")
        
        # إنشاء الجداول
        Base.metadata.create_all(bind=engine)
        print("✓ تم إنشاء الجداول")
        
        return True
        
    except Exception as e:
        print(f"✗ خطأ في هيكل المشروع: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_operations():
    """اختبار عمليات قاعدة البيانات"""
    print("\n=== اختبار عمليات قاعدة البيانات ===")
    
    try:
        from src.models.user import User
        from src.models.family import Family
        from src.models.individual import Individual
        from src.config.database import get_db_session
        from datetime import date
        
        db = get_db_session()
        
        # اختبار إنشاء مستخدم
        user = User(
            username="test_admin",
            full_name="مدير الاختبار",
            email="<EMAIL>",
            role="admin"
        )
        user.set_password("test123")
        
        # حذف المستخدم إذا كان موجوداً
        existing_user = db.query(User).filter(User.username == "test_admin").first()
        if existing_user:
            db.delete(existing_user)
            db.commit()
        
        db.add(user)
        db.commit()
        print("✓ تم إنشاء مستخدم اختبار")
        
        # اختبار تسجيل الدخول
        test_user = db.query(User).filter(User.username == "test_admin").first()
        if test_user and test_user.check_password("test123"):
            print("✓ تم التحقق من كلمة المرور")
        else:
            print("✗ فشل في التحقق من كلمة المرور")
            return False
        
        # اختبار إنشاء أسرة
        family = Family(
            family_id="TEST001",
            head_of_family_name="أحمد محمد الاختبار",
            head_birth_date=date(1980, 1, 15),
            head_gender="ذكر",
            head_marital_status="متزوج",
            phone_number="+970123456789",
            arrival_date=date.today(),
            tent_number="T-001",
            nationality="فلسطيني"
        )
        
        # حذف الأسرة إذا كانت موجودة
        existing_family = db.query(Family).filter(Family.family_id == "TEST001").first()
        if existing_family:
            db.delete(existing_family)
            db.commit()
        
        db.add(family)
        db.flush()
        
        # إضافة فرد للأسرة
        individual = Individual(
            family_id=family.id,
            full_name="فاطمة أحمد الاختبار",
            birth_date=date(2005, 3, 20),
            gender="أنثى",
            relationship_to_head="ابنة"
        )
        
        db.add(individual)
        db.commit()
        print("✓ تم إنشاء أسرة وفرد اختبار")
        
        # اختبار الاستعلام
        test_family = db.query(Family).filter(Family.family_id == "TEST001").first()
        if test_family:
            print(f"✓ تم العثور على الأسرة: {test_family.head_of_family_name}")
            print(f"✓ عدد الأفراد: {len(test_family.individuals)}")
        else:
            print("✗ لم يتم العثور على الأسرة")
            return False
        
        db.close()
        return True
        
    except Exception as e:
        print(f"✗ خطأ في عمليات قاعدة البيانات: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_services():
    """اختبار الخدمات"""
    print("\n=== اختبار الخدمات ===")
    
    try:
        # اختبار خدمة المصادقة
        from src.services.auth_service import AuthService
        
        auth = AuthService()
        auth.create_default_admin()
        print("✓ خدمة المصادقة")
        
        # اختبار تسجيل الدخول
        success, message, user = auth.login("admin", "admin123")
        if success:
            print("✓ تسجيل الدخول نجح")
        else:
            print(f"✗ فشل تسجيل الدخول: {message}")
        
        # اختبار خدمة الأسر
        from src.services.family_service import FamilyService
        
        family_service = FamilyService()
        stats = family_service.get_statistics()
        print(f"✓ خدمة الأسر - إحصائيات: {stats['total_families']} أسرة")
        
        return True
        
    except Exception as e:
        print(f"✗ خطأ في الخدمات: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_excel_service():
    """اختبار خدمة Excel"""
    print("\n=== اختبار خدمة Excel ===")
    
    try:
        from src.services.excel_service import ExcelService
        import tempfile
        import os
        
        excel_service = ExcelService()
        
        # إنشاء ملف مؤقت
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp:
            temp_file = tmp.name
        
        # إنشاء قالب
        success = excel_service.create_families_template(temp_file)
        if success:
            print("✓ تم إنشاء قالب Excel")
        else:
            print("✗ فشل في إنشاء قالب Excel")
            return False
        
        # تنظيف الملف المؤقت
        os.unlink(temp_file)
        
        return True
        
    except Exception as e:
        print(f"✗ خطأ في خدمة Excel: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_validators():
    """اختبار دوال التحقق"""
    print("\n=== اختبار دوال التحقق ===")
    
    try:
        from src.utils.validators import (
            validate_name, validate_phone_number, 
            validate_date, validate_family_data
        )
        
        # اختبار التحقق من الاسم
        valid, error = validate_name("أحمد محمد علي")
        if valid:
            print("✓ التحقق من الاسم")
        else:
            print(f"✗ فشل التحقق من الاسم: {error}")
        
        # اختبار التحقق من رقم الهاتف
        valid, error = validate_phone_number("+970123456789")
        if valid:
            print("✓ التحقق من رقم الهاتف")
        else:
            print(f"✗ فشل التحقق من رقم الهاتف: {error}")
        
        return True
        
    except Exception as e:
        print(f"✗ خطأ في دوال التحقق: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار الشامل"""
    print("🏕️ اختبار شامل لنظام إدارة المخيم")
    print("=" * 60)
    
    tests = [
        ("PyQt5", test_pyqt5),
        ("الوحدات الأساسية", test_basic_modules),
        ("هيكل المشروع", test_project_structure),
        ("عمليات قاعدة البيانات", test_database_operations),
        ("الخدمات", test_services),
        ("خدمة Excel", test_excel_service),
        ("دوال التحقق", test_validators)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            if result:
                passed += 1
                print(f"✅ {test_name} نجح")
            else:
                print(f"❌ {test_name} فشل")
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
        print("-" * 40)
    
    print("\n" + "=" * 60)
    print(f"📊 النتائج النهائية: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام.")
        print("\n📝 الخطوات التالية:")
        if test_pyqt5():
            print("1. تشغيل التطبيق: python src/main.py")
        else:
            print("1. إصلاح مشكلة PyQt5 أولاً")
            print("2. تشغيل التطبيق: python src/main.py")
        print("3. استخدام بيانات المدير: admin / admin123")
        return 0
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    input("\nاضغط Enter للخروج...")
    sys.exit(exit_code)
