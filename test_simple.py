"""
اختبار مبسط جداً للتطبيق
Very Simple Application Test
"""
import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_basic_setup():
    """اختبار الإعداد الأساسي"""
    print("=== اختبار الإعداد الأساسي ===")
    
    try:
        # اختبار استيراد SQLAlchemy
        import sqlalchemy
        print("✓ SQLAlchemy متوفر")
        
        # اختبار استيراد الإعدادات
        from src.config.settings import create_directories, BASE_DIR
        print("✓ إعدادات التطبيق")
        
        # إنشاء المجلدات
        create_directories()
        print("✓ تم إنشاء المجلدات")
        
        # اختبار قاعدة البيانات
        from src.config.database import init_database, Base, engine
        init_database()
        print("✓ تم تهيئة قاعدة البيانات")
        
        # اختبار النماذج
        from src.models.user import User
        from src.models.family import Family
        from src.models.individual import Individual
        print("✓ تم استيراد النماذج")
        
        # إنشاء الجداول
        Base.metadata.create_all(bind=engine)
        print("✓ تم إنشاء الجداول")
        
        return True
        
    except Exception as e:
        print(f"✗ خطأ: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_user_creation():
    """اختبار إنشاء مستخدم"""
    print("\n=== اختبار إنشاء مستخدم ===")
    
    try:
        from src.models.user import User
        from src.config.database import get_db_session
        
        db = get_db_session()
        
        # إنشاء مستخدم تجريبي
        user = User(
            username="admin",
            full_name="المدير الرئيسي",
            email="<EMAIL>",
            role="admin"
        )
        user.set_password("admin123")
        
        # حذف المستخدم إذا كان موجوداً
        existing_user = db.query(User).filter(User.username == "admin").first()
        if existing_user:
            db.delete(existing_user)
            db.commit()
        
        # إضافة المستخدم الجديد
        db.add(user)
        db.commit()
        
        print("✓ تم إنشاء المستخدم")
        
        # اختبار تسجيل الدخول
        test_user = db.query(User).filter(User.username == "admin").first()
        if test_user and test_user.check_password("admin123"):
            print("✓ تم التحقق من كلمة المرور")
        else:
            print("✗ فشل في التحقق من كلمة المرور")
            return False
        
        db.close()
        return True
        
    except Exception as e:
        print(f"✗ خطأ في إنشاء المستخدم: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_family_creation():
    """اختبار إنشاء أسرة"""
    print("\n=== اختبار إنشاء أسرة ===")
    
    try:
        from src.models.family import Family
        from src.models.individual import Individual
        from src.config.database import get_db_session
        from datetime import date
        
        db = get_db_session()
        
        # حذف الأسرة إذا كانت موجودة
        existing_family = db.query(Family).filter(Family.family_id == "FAM000001").first()
        if existing_family:
            db.delete(existing_family)
            db.commit()
        
        # إنشاء أسرة جديدة
        family = Family(
            family_id="FAM000001",
            head_of_family_name="أحمد محمد علي",
            head_birth_date=date(1980, 1, 15),
            head_gender="ذكر",
            head_marital_status="متزوج",
            phone_number="+970123456789",
            arrival_date=date.today(),
            tent_number="A-001",
            nationality="فلسطيني"
        )
        
        db.add(family)
        db.flush()  # للحصول على ID
        
        # إضافة فرد للأسرة
        individual = Individual(
            family_id=family.id,
            full_name="فاطمة أحمد محمد",
            birth_date=date(2005, 3, 20),
            gender="أنثى",
            relationship_to_head="ابنة"
        )
        
        db.add(individual)
        db.commit()
        
        print("✓ تم إنشاء الأسرة والفرد")
        
        # اختبار الاستعلام
        test_family = db.query(Family).filter(Family.family_id == "FAM000001").first()
        if test_family:
            print(f"✓ تم العثور على الأسرة: {test_family.head_of_family_name}")
            print(f"✓ عدد الأفراد: {len(test_family.individuals)}")
        else:
            print("✗ لم يتم العثور على الأسرة")
            return False
        
        db.close()
        return True
        
    except Exception as e:
        print(f"✗ خطأ في إنشاء الأسرة: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("🏕️ اختبار نظام إدارة المخيم - الإصدار المبسط")
    print("=" * 50)
    
    tests = [
        ("الإعداد الأساسي", test_basic_setup),
        ("إنشاء مستخدم", test_user_creation),
        ("إنشاء أسرة", test_family_creation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ فشل اختبار: {test_name}")
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 النتائج: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت!")
        print("\n📝 الخطوات التالية:")
        print("1. تثبيت PyQt5 للواجهة الرسومية")
        print("2. تشغيل التطبيق: python src/main.py")
        return 0
    else:
        print("⚠️ بعض الاختبارات فشلت")
        return 1

if __name__ == "__main__":
    exit_code = main()
    input("\nاضغط Enter للخروج...")
    sys.exit(exit_code)
