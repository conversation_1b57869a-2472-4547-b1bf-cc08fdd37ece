"""
اختبار سريع لواجهة Tkinter
Quick test for Tkinter GUI
"""
import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_tkinter_gui():
    """اختبار واجهة Tkinter"""
    print("🖥️ اختبار واجهة Tkinter")
    print("=" * 40)
    
    try:
        # اختبار Tkinter
        import tkinter as tk
        from tkinter import ttk
        print("✅ Tkinter متوفر")
        
        # اختبار إنشاء نافذة بسيطة
        root = tk.Tk()
        root.title("اختبار Tkinter")
        root.geometry("300x200")
        
        # إضافة عناصر
        ttk.Label(root, text="مرحباً! Tkinter يعمل بنجاح", 
                 font=("Arial", 12, "bold")).pack(pady=20)
        
        ttk.Button(root, text="إغلاق", 
                  command=root.destroy).pack(pady=10)
        
        print("✅ تم إنشاء نافذة اختبار")
        print("📝 ملاحظة: أغلق النافذة للمتابعة")
        
        # عرض النافذة
        root.mainloop()
        
        print("✅ تم إغلاق النافذة بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار Tkinter: {e}")
        return False

def test_login_window():
    """اختبار نافذة تسجيل الدخول"""
    print("\n🔐 اختبار نافذة تسجيل الدخول")
    print("=" * 40)
    
    try:
        from src.gui_tkinter.login_window import LoginWindow
        print("✅ تم استيراد نافذة تسجيل الدخول")
        
        # إنشاء النافذة (بدون تشغيل)
        login_window = LoginWindow()
        print("✅ تم إنشاء نافذة تسجيل الدخول")
        
        # إغلاق النافذة
        login_window.root.destroy()
        print("✅ تم إغلاق النافذة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نافذة تسجيل الدخول: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("🏕️ اختبار نظام إدارة المخيم - واجهة Tkinter")
    print("=" * 60)
    
    tests = [
        ("Tkinter الأساسي", test_tkinter_gui),
        ("نافذة تسجيل الدخول", test_login_window)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} نجح")
            else:
                print(f"❌ {test_name} فشل")
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
        print("-" * 40)
    
    print(f"\n📊 النتائج: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت!")
        print("\n📝 لتشغيل التطبيق:")
        print("python tkinter_app.py")
        return 0
    else:
        print("⚠️ بعض الاختبارات فشلت")
        return 1

if __name__ == "__main__":
    exit_code = main()
    input("\nاضغط Enter للخروج...")
    sys.exit(exit_code)
