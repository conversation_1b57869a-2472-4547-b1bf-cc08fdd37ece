"""
تطبيق نظام إدارة المخيم باستخدام Tkinter
Camp Management System using Tkinter
"""
import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import tkinter as tk
from tkinter import messagebox

# استيراد الوحدات المطلوبة
from src.config.database import create_tables, init_database
from src.config.settings import create_directories
from src.gui_tkinter.login_window import LoginWindow

class CampManagementTkinterApp:
    """التطبيق الرئيسي لنظام إدارة المخيم باستخدام Tkinter"""
    
    def __init__(self):
        self.login_window = None
        
    def initialize_app(self):
        """تهيئة التطبيق"""
        try:
            # إنشاء المجلدات المطلوبة
            create_directories()
            
            # تهيئة قاعدة البيانات
            init_database()
            
            # إنشاء الجداول
            create_tables()
            
            return True
            
        except Exception as e:
            messagebox.showerror(
                "خطأ في التهيئة",
                f"فشل في إعداد التطبيق:\n{str(e)}\n\n"
                "يرجى التأكد من صلاحيات الكتابة في مجلد التطبيق."
            )
            return False
    
    def run(self):
        """تشغيل التطبيق"""
        try:
            # تهيئة التطبيق
            if not self.initialize_app():
                return 1
            
            # إظهار نافذة تسجيل الدخول
            self.login_window = LoginWindow()
            self.login_window.run()
            
            return 0
            
        except KeyboardInterrupt:
            print("\nتم إيقاف التطبيق بواسطة المستخدم")
            return 0
            
        except Exception as e:
            messagebox.showerror(
                "خطأ فادح",
                f"حدث خطأ غير متوقع:\n{str(e)}\n\n"
                "سيتم إغلاق التطبيق."
            )
            return 1

def main():
    """الدالة الرئيسية"""
    try:
        # إنشاء وتشغيل التطبيق
        app = CampManagementTkinterApp()
        exit_code = app.run()
        
        print(f"تم إنهاء التطبيق برمز الخروج: {exit_code}")
        sys.exit(exit_code)
        
    except Exception as e:
        print(f"خطأ فادح في التطبيق: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
